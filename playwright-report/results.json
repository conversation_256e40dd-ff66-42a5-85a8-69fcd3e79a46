{"config": {"configFile": "/Users/<USER>/Github/vocab/playwright.config.ts", "rootDir": "/Users/<USER>/Github/vocab/e2e", "forbidOnly": false, "fullyParallel": true, "globalSetup": "/Users/<USER>/Github/vocab/e2e/global-setup.ts", "globalTeardown": "/Users/<USER>/Github/vocab/e2e/global-teardown.ts", "globalTimeout": 0, "grep": {}, "grepInvert": null, "maxFailures": 0, "metadata": {"actualWorkers": 1}, "preserveOutput": "always", "reporter": [["html", null], ["json", {"outputFile": "playwright-report/results.json"}], ["junit", {"outputFile": "playwright-report/results.xml"}]], "reportSlowTests": {"max": 5, "threshold": 300000}, "quiet": false, "projects": [{"outputDir": "/Users/<USER>/Github/vocab/playwright-report", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 1}, "id": "chromium", "name": "chromium", "testDir": "/Users/<USER>/Github/vocab/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 60000}, {"outputDir": "/Users/<USER>/Github/vocab/playwright-report", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 1}, "id": "firefox", "name": "firefox", "testDir": "/Users/<USER>/Github/vocab/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 60000}, {"outputDir": "/Users/<USER>/Github/vocab/playwright-report", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 1}, "id": "webkit", "name": "webkit", "testDir": "/Users/<USER>/Github/vocab/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 60000}, {"outputDir": "/Users/<USER>/Github/vocab/playwright-report", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 1}, "id": "Mobile Chrome", "name": "Mobile Chrome", "testDir": "/Users/<USER>/Github/vocab/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 60000}, {"outputDir": "/Users/<USER>/Github/vocab/playwright-report", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 1}, "id": "Mobile Safari", "name": "Mobile Safari", "testDir": "/Users/<USER>/Github/vocab/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 60000}], "shard": null, "updateSnapshots": "missing", "updateSourceMethod": "patch", "version": "1.53.1", "workers": 4, "webServer": {"command": "yarn dev", "url": "http://localhost:3000", "reuseExistingServer": true, "timeout": 120000}}, "suites": [{"title": "test-simple-guidance.spec.ts", "file": "test-simple-guidance.spec.ts", "column": 0, "line": 0, "specs": [], "suites": [{"title": "Test Simple Guidance", "file": "test-simple-guidance.spec.ts", "line": 3, "column": 6, "specs": [{"title": "should show SimplePageGuidance on my-words page", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "passed", "duration": 6929, "errors": [], "stdout": [{"text": "Found 0 collection links\n"}, {"text": "❌ No collections found\n"}], "stderr": [], "retry": 0, "startTime": "2025-07-18T11:32:44.103Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "2becb5515d9937abac54-2075b34eb3ee4a1e30ac", "file": "test-simple-guidance.spec.ts", "line": 4, "column": 6}]}]}], "errors": [], "stats": {"startTime": "2025-07-18T11:32:43.179Z", "duration": 8832.************, "expected": 1, "skipped": 0, "unexpected": 0, "flaky": 0}}