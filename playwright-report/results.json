{"config": {"configFile": "/Users/<USER>/Github/vocab/playwright.config.ts", "rootDir": "/Users/<USER>/Github/vocab/e2e", "forbidOnly": false, "fullyParallel": true, "globalSetup": "/Users/<USER>/Github/vocab/e2e/global-setup.ts", "globalTeardown": "/Users/<USER>/Github/vocab/e2e/global-teardown.ts", "globalTimeout": 0, "grep": {}, "grepInvert": null, "maxFailures": 0, "metadata": {"actualWorkers": 1}, "preserveOutput": "always", "reporter": [["html", null], ["json", {"outputFile": "playwright-report/results.json"}], ["junit", {"outputFile": "playwright-report/results.xml"}]], "reportSlowTests": {"max": 5, "threshold": 300000}, "quiet": false, "projects": [{"outputDir": "/Users/<USER>/Github/vocab/playwright-report", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 1}, "id": "chromium", "name": "chromium", "testDir": "/Users/<USER>/Github/vocab/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 60000}, {"outputDir": "/Users/<USER>/Github/vocab/playwright-report", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 1}, "id": "firefox", "name": "firefox", "testDir": "/Users/<USER>/Github/vocab/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 60000}, {"outputDir": "/Users/<USER>/Github/vocab/playwright-report", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 1}, "id": "webkit", "name": "webkit", "testDir": "/Users/<USER>/Github/vocab/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 60000}, {"outputDir": "/Users/<USER>/Github/vocab/playwright-report", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 1}, "id": "Mobile Chrome", "name": "Mobile Chrome", "testDir": "/Users/<USER>/Github/vocab/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 60000}, {"outputDir": "/Users/<USER>/Github/vocab/playwright-report", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 1}, "id": "Mobile Safari", "name": "Mobile Safari", "testDir": "/Users/<USER>/Github/vocab/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 60000}], "shard": null, "updateSnapshots": "missing", "updateSourceMethod": "patch", "version": "1.53.1", "workers": 4, "webServer": {"command": "yarn dev", "url": "http://localhost:3000", "reuseExistingServer": true, "timeout": 120000}}, "suites": [{"title": "test-guidance-final.spec.ts", "file": "test-guidance-final.spec.ts", "column": 0, "line": 0, "specs": [], "suites": [{"title": "Test Guidance Final", "file": "test-guidance-final.spec.ts", "line": 3, "column": 6, "specs": [{"title": "should test guidance button click and panel open/close", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "failed", "duration": 19376, "error": {"message": "TimeoutError: locator.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('.fixed.inset-0').first()\u001b[22m\n\u001b[2m    - locator resolved to <div class=\"fixed inset-0 bg-black/20 backdrop-blur-sm z-[1000]\"></div>\u001b[22m\n\u001b[2m  - attempting click action\u001b[22m\n\u001b[2m    2 × waiting for element to be visible, enabled and stable\u001b[22m\n\u001b[2m      - element is visible, enabled and stable\u001b[22m\n\u001b[2m      - scrolling into view if needed\u001b[22m\n\u001b[2m      - done scrolling\u001b[22m\n\u001b[2m      - <p class=\"text-sm text-gray-700 dark:text-gray-300 leading-relaxed\">Step 2: This is another test step</p> from <div class=\"fixed inset-0 z-[1001] flex items-center justify-center p-4\">…</div> subtree intercepts pointer events\u001b[22m\n\u001b[2m    - retrying click action\u001b[22m\n\u001b[2m    - waiting 20ms\u001b[22m\n\u001b[2m    2 × waiting for element to be visible, enabled and stable\u001b[22m\n\u001b[2m      - element is visible, enabled and stable\u001b[22m\n\u001b[2m      - scrolling into view if needed\u001b[22m\n\u001b[2m      - done scrolling\u001b[22m\n\u001b[2m      - <p class=\"text-sm text-gray-700 dark:text-gray-300 leading-relaxed\">Step 2: This is another test step</p> from <div class=\"fixed inset-0 z-[1001] flex items-center justify-center p-4\">…</div> subtree intercepts pointer events\u001b[22m\n\u001b[2m    - retrying click action\u001b[22m\n\u001b[2m      - waiting 100ms\u001b[22m\n\u001b[2m    2 × waiting for element to be visible, enabled and stable\u001b[22m\n\u001b[2m      - element is visible, enabled and stable\u001b[22m\n\u001b[2m      - scrolling into view if needed\u001b[22m\n\u001b[2m      - done scrolling\u001b[22m\n\u001b[2m      - <p class=\"text-sm text-gray-700 dark:text-gray-300 leading-relaxed\">Step 2: This is another test step</p> from <div class=\"fixed inset-0 z-[1001] flex items-center justify-center p-4\">…</div> subtree intercepts pointer events\u001b[22m\n\u001b[2m    - retrying click action\u001b[22m\n\u001b[2m      - waiting 500ms\u001b[22m\n\u001b[2m    - waiting for element to be visible, enabled and stable\u001b[22m\n\u001b[2m  - element was detached from the DOM, retrying\u001b[22m\n", "stack": "TimeoutError: locator.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('.fixed.inset-0').first()\u001b[22m\n\u001b[2m    - locator resolved to <div class=\"fixed inset-0 bg-black/20 backdrop-blur-sm z-[1000]\"></div>\u001b[22m\n\u001b[2m  - attempting click action\u001b[22m\n\u001b[2m    2 × waiting for element to be visible, enabled and stable\u001b[22m\n\u001b[2m      - element is visible, enabled and stable\u001b[22m\n\u001b[2m      - scrolling into view if needed\u001b[22m\n\u001b[2m      - done scrolling\u001b[22m\n\u001b[2m      - <p class=\"text-sm text-gray-700 dark:text-gray-300 leading-relaxed\">Step 2: This is another test step</p> from <div class=\"fixed inset-0 z-[1001] flex items-center justify-center p-4\">…</div> subtree intercepts pointer events\u001b[22m\n\u001b[2m    - retrying click action\u001b[22m\n\u001b[2m    - waiting 20ms\u001b[22m\n\u001b[2m    2 × waiting for element to be visible, enabled and stable\u001b[22m\n\u001b[2m      - element is visible, enabled and stable\u001b[22m\n\u001b[2m      - scrolling into view if needed\u001b[22m\n\u001b[2m      - done scrolling\u001b[22m\n\u001b[2m      - <p class=\"text-sm text-gray-700 dark:text-gray-300 leading-relaxed\">Step 2: This is another test step</p> from <div class=\"fixed inset-0 z-[1001] flex items-center justify-center p-4\">…</div> subtree intercepts pointer events\u001b[22m\n\u001b[2m    - retrying click action\u001b[22m\n\u001b[2m      - waiting 100ms\u001b[22m\n\u001b[2m    2 × waiting for element to be visible, enabled and stable\u001b[22m\n\u001b[2m      - element is visible, enabled and stable\u001b[22m\n\u001b[2m      - scrolling into view if needed\u001b[22m\n\u001b[2m      - done scrolling\u001b[22m\n\u001b[2m      - <p class=\"text-sm text-gray-700 dark:text-gray-300 leading-relaxed\">Step 2: This is another test step</p> from <div class=\"fixed inset-0 z-[1001] flex items-center justify-center p-4\">…</div> subtree intercepts pointer events\u001b[22m\n\u001b[2m    - retrying click action\u001b[22m\n\u001b[2m      - waiting 500ms\u001b[22m\n\u001b[2m    - waiting for element to be visible, enabled and stable\u001b[22m\n\u001b[2m  - element was detached from the DOM, retrying\u001b[22m\n\n    at /Users/<USER>/Github/vocab/e2e/test-guidance-final.spec.ts:101:21", "location": {"file": "/Users/<USER>/Github/vocab/e2e/test-guidance-final.spec.ts", "column": 21, "line": 101}, "snippet": "\u001b[0m \u001b[90m  99 |\u001b[39m \t\t\t\t\u001b[36mif\u001b[39m (backdropCount \u001b[33m>\u001b[39m \u001b[35m0\u001b[39m) {\n \u001b[90m 100 |\u001b[39m \t\t\t\t\tconsole\u001b[33m.\u001b[39mlog(\u001b[32m'Trying to close by clicking backdrop...'\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 101 |\u001b[39m \t\t\t\t\t\u001b[36mawait\u001b[39m backdrop\u001b[33m.\u001b[39mclick()\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m \t\t\t\t\t               \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 102 |\u001b[39m \t\t\t\t\t\u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForTimeout(\u001b[35m1000\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 103 |\u001b[39m \t\t\t\t\t\n \u001b[90m 104 |\u001b[39m \t\t\t\t\t\u001b[90m// Check if panel closed\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Github/vocab/e2e/test-guidance-final.spec.ts", "column": 21, "line": 101}, "message": "TimeoutError: locator.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('.fixed.inset-0').first()\u001b[22m\n\u001b[2m    - locator resolved to <div class=\"fixed inset-0 bg-black/20 backdrop-blur-sm z-[1000]\"></div>\u001b[22m\n\u001b[2m  - attempting click action\u001b[22m\n\u001b[2m    2 × waiting for element to be visible, enabled and stable\u001b[22m\n\u001b[2m      - element is visible, enabled and stable\u001b[22m\n\u001b[2m      - scrolling into view if needed\u001b[22m\n\u001b[2m      - done scrolling\u001b[22m\n\u001b[2m      - <p class=\"text-sm text-gray-700 dark:text-gray-300 leading-relaxed\">Step 2: This is another test step</p> from <div class=\"fixed inset-0 z-[1001] flex items-center justify-center p-4\">…</div> subtree intercepts pointer events\u001b[22m\n\u001b[2m    - retrying click action\u001b[22m\n\u001b[2m    - waiting 20ms\u001b[22m\n\u001b[2m    2 × waiting for element to be visible, enabled and stable\u001b[22m\n\u001b[2m      - element is visible, enabled and stable\u001b[22m\n\u001b[2m      - scrolling into view if needed\u001b[22m\n\u001b[2m      - done scrolling\u001b[22m\n\u001b[2m      - <p class=\"text-sm text-gray-700 dark:text-gray-300 leading-relaxed\">Step 2: This is another test step</p> from <div class=\"fixed inset-0 z-[1001] flex items-center justify-center p-4\">…</div> subtree intercepts pointer events\u001b[22m\n\u001b[2m    - retrying click action\u001b[22m\n\u001b[2m      - waiting 100ms\u001b[22m\n\u001b[2m    2 × waiting for element to be visible, enabled and stable\u001b[22m\n\u001b[2m      - element is visible, enabled and stable\u001b[22m\n\u001b[2m      - scrolling into view if needed\u001b[22m\n\u001b[2m      - done scrolling\u001b[22m\n\u001b[2m      - <p class=\"text-sm text-gray-700 dark:text-gray-300 leading-relaxed\">Step 2: This is another test step</p> from <div class=\"fixed inset-0 z-[1001] flex items-center justify-center p-4\">…</div> subtree intercepts pointer events\u001b[22m\n\u001b[2m    - retrying click action\u001b[22m\n\u001b[2m      - waiting 500ms\u001b[22m\n\u001b[2m    - waiting for element to be visible, enabled and stable\u001b[22m\n\u001b[2m  - element was detached from the DOM, retrying\u001b[22m\n\n\n\u001b[0m \u001b[90m  99 |\u001b[39m \t\t\t\t\u001b[36mif\u001b[39m (backdropCount \u001b[33m>\u001b[39m \u001b[35m0\u001b[39m) {\n \u001b[90m 100 |\u001b[39m \t\t\t\t\tconsole\u001b[33m.\u001b[39mlog(\u001b[32m'Trying to close by clicking backdrop...'\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 101 |\u001b[39m \t\t\t\t\t\u001b[36mawait\u001b[39m backdrop\u001b[33m.\u001b[39mclick()\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m \t\t\t\t\t               \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 102 |\u001b[39m \t\t\t\t\t\u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForTimeout(\u001b[35m1000\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 103 |\u001b[39m \t\t\t\t\t\n \u001b[90m 104 |\u001b[39m \t\t\t\t\t\u001b[90m// Check if panel closed\u001b[39m\u001b[0m\n\u001b[2m    at /Users/<USER>/Github/vocab/e2e/test-guidance-final.spec.ts:101:21\u001b[22m"}], "stdout": [{"text": "Navigating to: http://localhost:3001/test-guidance\n"}, {"text": "Found 4 floating buttons total\n"}, {"text": "Button 1: { title: \u001b[32m'Open settings'\u001b[39m, ariaLabel: \u001b[32m'Open settings'\u001b[39m }\n"}, {"text": "Button 2: { title: \u001b[32m'Send us your feedback'\u001b[39m, <PERSON><PERSON><PERSON><PERSON><PERSON>: \u001b[32m'Send us your feedback'\u001b[39m }\n"}, {"text": "Button 3: { title: \u001b[32m'Open page guidance'\u001b[39m, a<PERSON><PERSON><PERSON><PERSON>: \u001b[32m'Open page guidance'\u001b[39m }\n"}, {"text": "✅ Found guidance button at index 2\n"}, {"text": "✅ Guidance button found! Testing click...\n"}, {"text": "\n=== Console logs after guidance button click ===\n"}, {"text": "log: [Fast Refresh] rebuilding\n"}, {"text": "log: [Fast Refresh] done in 119ms\n"}, {"text": "log: SimpleEnhancedFloatingButtons render: {includeGuidance: true, guidanceConfig: Object, isGuidanceOpen: false}\n"}, {"text": "log: Guidance button clicked\n"}, {"text": "log: toggleGuidance called {config: Object, isOpen: false}\n"}, {"text": "log: Setting isOpen to: true\n"}, {"text": "log: SimpleEnhancedFloatingButtons render: {includeGuidance: true, guidanceConfig: Object, isGuidanceOpen: true}\n"}, {"text": "warning: Translation key not found: Test Guidance\n"}, {"text": "warning: Translation key not found: Step 1: This is a test step\n"}, {"text": "warning: Translation key not found: Step 2: This is another test step\n"}, {"text": "warning: Translation key not found: Step 3: This is the final test step\n"}, {"text": "warning: Translation key not found: guidance.tip\n"}, {"text": "warning: Translation key not found: This is a test tip\n"}, {"text": "log: SimpleEnhancedFloatingButtons render: {includeGuidance: true, guidanceConfig: Object, isGuidanceOpen: false}\n"}, {"text": "log: Guidance button clicked\n"}, {"text": "log: toggleGuidance called {config: Object, isOpen: false}\n"}, {"text": "log: Setting isOpen to: true\n"}, {"text": "log: SimpleEnhancedFloatingButtons render: {includeGuidance: true, guidanceConfig: Object, isGuidanceOpen: true}\n"}, {"text": "warning: Translation key not found: Test Guidance\n"}, {"text": "warning: Translation key not found: Step 1: This is a test step\n"}, {"text": "warning: Translation key not found: Step 2: This is another test step\n"}, {"text": "warning: Translation key not found: Step 3: This is the final test step\n"}, {"text": "warning: Translation key not found: guidance.tip\n"}, {"text": "warning: Translation key not found: This is a test tip\n"}, {"text": "log: SimpleEnhancedFloatingButtons render: {includeGuidance: true, guidanceConfig: Object, isGuidanceOpen: false}\n"}, {"text": "log: Guidance button clicked\n"}, {"text": "log: toggleGuidance called {config: Object, isOpen: false}\n"}, {"text": "log: Setting isOpen to: true\n"}, {"text": "log: SimpleEnhancedFloatingButtons render: {includeGuidance: true, guidanceConfig: Object, isGuidanceOpen: true}\n"}, {"text": "warning: Translation key not found: Test Guidance\n"}, {"text": "warning: Translation key not found: Step 1: This is a test step\n"}, {"text": "warning: Translation key not found: Step 2: This is another test step\n"}, {"text": "warning: Translation key not found: Step 3: This is the final test step\n"}, {"text": "warning: Translation key not found: guidance.tip\n"}, {"text": "warning: Translation key not found: This is a test tip\n"}, {"text": "Selector \"[role=\"dialog\"]\": 0 elements found\n"}, {"text": "Selector \".card\": 0 elements found\n"}, {"text": "Selector \"[class*=\"guidance\"]\": 0 elements found\n"}, {"text": "Selector \"text=\"Test Guidance\"\": 1 elements found\n"}, {"text": "Selector \"text=\"Step 1\"\": 0 elements found\n"}, {"text": "Selector \"text=\"Step 2\"\": 0 elements found\n"}, {"text": "Selector \"text=\"Step 3\"\": 0 elements found\n"}, {"text": "Selector \".fixed.inset-0\": 2 elements found\n"}, {"text": "✅ Guidance panel opened successfully!\n"}, {"text": "Trying to close by clicking backdrop...\n"}], "stderr": [], "retry": 0, "startTime": "2025-07-18T11:54:49.897Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Github/vocab/playwright-report/test-guidance-final-Test-G-ff735--click-and-panel-open-close-chromium/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Github/vocab/playwright-report/test-guidance-final-Test-G-ff735--click-and-panel-open-close-chromium/video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Users/<USER>/Github/vocab/playwright-report/test-guidance-final-Test-G-ff735--click-and-panel-open-close-chromium/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Github/vocab/e2e/test-guidance-final.spec.ts", "column": 21, "line": 101}}], "status": "unexpected"}], "id": "f4f7d1ea6064181f720e-c024264637eeb26a697b", "file": "test-guidance-final.spec.ts", "line": 4, "column": 6}]}]}], "errors": [], "stats": {"startTime": "2025-07-18T11:54:48.902Z", "duration": 21344.************, "expected": 0, "skipped": 0, "unexpected": 1, "flaky": 0}}