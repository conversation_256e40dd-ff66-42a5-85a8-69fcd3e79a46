<testsuites id="" name="" tests="1" failures="0" skipped="0" errors="0" time="18.614496000000003">
<testsuite name="test-guidance-toggle.spec.ts" timestamp="2025-07-18T11:46:46.586Z" hostname="chromium" tests="1" failures="0" skipped="0" time="15.236" errors="0">
<testcase name="Test Guidance Toggle › should be able to open and close guidance" classname="test-guidance-toggle.spec.ts" time="15.236">
<system-out>
<![CDATA[Found 0 collection links
❌ No collections found
]]>
</system-out>
</testcase>
</testsuite>
</testsuites>