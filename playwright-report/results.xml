<testsuites id="" name="" tests="1" failures="1" skipped="0" errors="0" time="21.344618">
<testsuite name="test-guidance-final.spec.ts" timestamp="2025-07-18T11:54:49.624Z" hostname="chromium" tests="1" failures="1" skipped="0" time="19.376" errors="0">
<testcase name="Test Guidance Final › should test guidance button click and panel open/close" classname="test-guidance-final.spec.ts" time="19.376">
<failure message="test-guidance-final.spec.ts:4:6 should test guidance button click and panel open/close" type="FAILURE">
<![CDATA[  [chromium] › test-guidance-final.spec.ts:4:6 › Test Guidance Final › should test guidance button click and panel open/close 

    TimeoutError: locator.click: Timeout 10000ms exceeded.
    Call log:
      - waiting for locator('.fixed.inset-0').first()
        - locator resolved to <div class="fixed inset-0 bg-black/20 backdrop-blur-sm z-[1000]"></div>
      - attempting click action
        2 × waiting for element to be visible, enabled and stable
          - element is visible, enabled and stable
          - scrolling into view if needed
          - done scrolling
          - <p class="text-sm text-gray-700 dark:text-gray-300 leading-relaxed">Step 2: This is another test step</p> from <div class="fixed inset-0 z-[1001] flex items-center justify-center p-4">…</div> subtree intercepts pointer events
        - retrying click action
        - waiting 20ms
        2 × waiting for element to be visible, enabled and stable
          - element is visible, enabled and stable
          - scrolling into view if needed
          - done scrolling
          - <p class="text-sm text-gray-700 dark:text-gray-300 leading-relaxed">Step 2: This is another test step</p> from <div class="fixed inset-0 z-[1001] flex items-center justify-center p-4">…</div> subtree intercepts pointer events
        - retrying click action
          - waiting 100ms
        2 × waiting for element to be visible, enabled and stable
          - element is visible, enabled and stable
          - scrolling into view if needed
          - done scrolling
          - <p class="text-sm text-gray-700 dark:text-gray-300 leading-relaxed">Step 2: This is another test step</p> from <div class="fixed inset-0 z-[1001] flex items-center justify-center p-4">…</div> subtree intercepts pointer events
        - retrying click action
          - waiting 500ms
        - waiting for element to be visible, enabled and stable
      - element was detached from the DOM, retrying


       99 | 				if (backdropCount > 0) {
      100 | 					console.log('Trying to close by clicking backdrop...');
    > 101 | 					await backdrop.click();
          | 					               ^
      102 | 					await page.waitForTimeout(1000);
      103 | 					
      104 | 					// Check if panel closed
        at /Users/<USER>/Github/vocab/e2e/test-guidance-final.spec.ts:101:21

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    playwright-report/test-guidance-final-Test-G-ff735--click-and-panel-open-close-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    playwright-report/test-guidance-final-Test-G-ff735--click-and-panel-open-close-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../playwright-report/test-guidance-final-Test-G-ff735--click-and-panel-open-close-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[Navigating to: http://localhost:3001/test-guidance
Found 4 floating buttons total
Button 1: { title: [32m'Open settings'[39m, ariaLabel: [32m'Open settings'[39m }
Button 2: { title: [32m'Send us your feedback'[39m, ariaLabel: [32m'Send us your feedback'[39m }
Button 3: { title: [32m'Open page guidance'[39m, ariaLabel: [32m'Open page guidance'[39m }
✅ Found guidance button at index 2
✅ Guidance button found! Testing click...

=== Console logs after guidance button click ===
log: [Fast Refresh] rebuilding
log: [Fast Refresh] done in 119ms
log: SimpleEnhancedFloatingButtons render: {includeGuidance: true, guidanceConfig: Object, isGuidanceOpen: false}
log: Guidance button clicked
log: toggleGuidance called {config: Object, isOpen: false}
log: Setting isOpen to: true
log: SimpleEnhancedFloatingButtons render: {includeGuidance: true, guidanceConfig: Object, isGuidanceOpen: true}
warning: Translation key not found: Test Guidance
warning: Translation key not found: Step 1: This is a test step
warning: Translation key not found: Step 2: This is another test step
warning: Translation key not found: Step 3: This is the final test step
warning: Translation key not found: guidance.tip
warning: Translation key not found: This is a test tip
log: SimpleEnhancedFloatingButtons render: {includeGuidance: true, guidanceConfig: Object, isGuidanceOpen: false}
log: Guidance button clicked
log: toggleGuidance called {config: Object, isOpen: false}
log: Setting isOpen to: true
log: SimpleEnhancedFloatingButtons render: {includeGuidance: true, guidanceConfig: Object, isGuidanceOpen: true}
warning: Translation key not found: Test Guidance
warning: Translation key not found: Step 1: This is a test step
warning: Translation key not found: Step 2: This is another test step
warning: Translation key not found: Step 3: This is the final test step
warning: Translation key not found: guidance.tip
warning: Translation key not found: This is a test tip
log: SimpleEnhancedFloatingButtons render: {includeGuidance: true, guidanceConfig: Object, isGuidanceOpen: false}
log: Guidance button clicked
log: toggleGuidance called {config: Object, isOpen: false}
log: Setting isOpen to: true
log: SimpleEnhancedFloatingButtons render: {includeGuidance: true, guidanceConfig: Object, isGuidanceOpen: true}
warning: Translation key not found: Test Guidance
warning: Translation key not found: Step 1: This is a test step
warning: Translation key not found: Step 2: This is another test step
warning: Translation key not found: Step 3: This is the final test step
warning: Translation key not found: guidance.tip
warning: Translation key not found: This is a test tip
Selector "[role="dialog"]": 0 elements found
Selector ".card": 0 elements found
Selector "[class*="guidance"]": 0 elements found
Selector "text="Test Guidance"": 1 elements found
Selector "text="Step 1"": 0 elements found
Selector "text="Step 2"": 0 elements found
Selector "text="Step 3"": 0 elements found
Selector ".fixed.inset-0": 2 elements found
✅ Guidance panel opened successfully!
Trying to close by clicking backdrop...
]]>
</system-out>
<system-err>
<![CDATA[
Warning: attachment test-guidance-final-Test-G-ff735--click-and-panel-open-close-chromium/test-failed-1.png is missing
Warning: attachment test-guidance-final-Test-G-ff735--click-and-panel-open-close-chromium/video.webm is missing
Warning: attachment test-guidance-final-Test-G-ff735--click-and-panel-open-close-chromium/error-context.md is missing]]>
</system-err>
</testcase>
</testsuite>
</testsuites>