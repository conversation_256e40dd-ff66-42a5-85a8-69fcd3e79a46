import { test, expect } from '@playwright/test';

test.describe('Test Simple Guidance', () => {
	test('should show SimplePageGuidance on my-words page', async ({ page }) => {
		// Navigate to localhost:3001
		await page.goto('http://localhost:3001', { 
			waitUntil: 'networkidle',
			timeout: 15000 
		});
		
		await page.waitForTimeout(2000);
		
		// Navigate to collections
		await page.goto('http://localhost:3001/collections', { 
			waitUntil: 'networkidle',
			timeout: 15000 
		});
		
		await page.waitForTimeout(2000);
		
		// Look for any collection link
		const collectionLinks = page.locator('a[href*="/collections/"]');
		const collectionCount = await collectionLinks.count();
		console.log(`Found ${collectionCount} collection links`);
		
		if (collectionCount > 0) {
			// Click first collection
			await collectionLinks.first().click();
			await page.waitForTimeout(2000);
			
			// Navigate to my-words
			await page.goto(page.url() + '/vocabulary/my-words', { 
				waitUntil: 'networkidle',
				timeout: 15000 
			});
			
			await page.waitForTimeout(3000);
			
			// Take screenshot
			await page.screenshot({ path: 'test-results/my-words-with-simple-guidance.png', fullPage: true });
			
			// Check for guidance button (HelpCircle icon)
			const guidanceButton = page.locator('button').filter({ 
				has: page.locator('svg[class*="lucide-help-circle"]')
			});
			
			const guidanceButtonCount = await guidanceButton.count();
			console.log(`Found ${guidanceButtonCount} guidance buttons`);
			
			if (guidanceButtonCount > 0) {
				console.log('✅ SimplePageGuidance button found!');
				
				// Try to click the guidance button
				await guidanceButton.first().click();
				await page.waitForTimeout(1000);
				
				// Take screenshot with guidance open
				await page.screenshot({ path: 'test-results/my-words-guidance-open.png', fullPage: true });
				
				// Check for guidance content
				const guidanceCard = page.locator('[role="dialog"], .card, [class*="card"]').filter({
					hasText: 'guidance'
				});
				
				const guidanceCardCount = await guidanceCard.count();
				console.log(`Found ${guidanceCardCount} guidance cards`);
				
				if (guidanceCardCount > 0) {
					console.log('✅ SimplePageGuidance content found!');
				} else {
					console.log('❌ SimplePageGuidance content not found');
				}
			} else {
				console.log('❌ SimplePageGuidance button not found');
				
				// Check for any fixed position elements
				const fixedElements = page.locator('[style*="position: fixed"]');
				const fixedCount = await fixedElements.count();
				console.log(`Found ${fixedCount} fixed position elements`);
				
				// Check for any buttons at bottom right
				const bottomRightButtons = page.locator('button').filter({
					has: page.locator('svg')
				});
				const bottomRightCount = await bottomRightButtons.count();
				console.log(`Found ${bottomRightCount} buttons with icons`);
			}
		} else {
			console.log('❌ No collections found');
		}
	});
});
