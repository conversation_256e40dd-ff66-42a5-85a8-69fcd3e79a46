import { test, expect } from '@playwright/test';

test.describe('Basic E2E Tests', () => {
	test('should load homepage', async ({ page }) => {
		await page.goto('/');

		// Check if the page loads
		await expect(page).toHaveTitle(/Vocab/);
	});

	test('should have working API', async ({ page }) => {
		const response = await page.goto('/api/auth/status');
		expect(response?.status()).toBe(401); // Unauthorized is expected for unauthenticated requests
	});
});
