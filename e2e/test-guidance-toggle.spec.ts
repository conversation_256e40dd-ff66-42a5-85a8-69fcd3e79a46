import { test, expect } from '@playwright/test';

test.describe('Test Guidance Toggle', () => {
	test('should be able to open and close guidance', async ({ page }) => {
		// Navigate to localhost:3001
		await page.goto('http://localhost:3001', { 
			waitUntil: 'networkidle',
			timeout: 15000 
		});
		
		await page.waitForTimeout(2000);
		
		// Navigate to collections
		await page.goto('http://localhost:3001/collections', { 
			waitUntil: 'networkidle',
			timeout: 15000 
		});
		
		await page.waitForTimeout(2000);
		
		// Look for any collection link
		const collectionLinks = page.locator('a[href*="/collections/"]');
		const collectionCount = await collectionLinks.count();
		console.log(`Found ${collectionCount} collection links`);
		
		if (collectionCount > 0) {
			// Click first collection
			await collectionLinks.first().click();
			await page.waitForTimeout(2000);
			
			// Navigate to my-words
			await page.goto(page.url() + '/vocabulary/my-words', { 
				waitUntil: 'networkidle',
				timeout: 15000 
			});
			
			await page.waitForTimeout(3000);
			
			// Take screenshot
			await page.screenshot({ path: 'test-results/guidance-toggle-initial.png', fullPage: true });
			
			// Check for guidance button (HelpCircle icon)
			const guidanceButton = page.locator('button').filter({ 
				has: page.locator('svg[class*="lucide-help-circle"]')
			});
			
			const guidanceButtonCount = await guidanceButton.count();
			console.log(`Found ${guidanceButtonCount} guidance buttons`);
			
			if (guidanceButtonCount > 0) {
				console.log('✅ Guidance button found!');
				
				// Click to open guidance
				await guidanceButton.first().click();
				await page.waitForTimeout(1000);
				
				// Take screenshot with guidance open
				await page.screenshot({ path: 'test-results/guidance-open.png', fullPage: true });
				
				// Check for guidance panel
				const guidancePanel = page.locator('[role="dialog"], .card').filter({
					hasText: /guidance|hướng dẫn/i
				});
				
				const guidancePanelCount = await guidancePanel.count();
				console.log(`Found ${guidancePanelCount} guidance panels`);
				
				if (guidancePanelCount > 0) {
					console.log('✅ Guidance panel opened!');
					
					// Try to close guidance by clicking X button
					const closeButton = page.locator('button').filter({ 
						has: page.locator('svg[class*="lucide-x"]') 
					}).first();
					
					const closeButtonCount = await closeButton.count();
					console.log(`Found ${closeButtonCount} close buttons`);
					
					if (closeButtonCount > 0) {
						await closeButton.click();
						await page.waitForTimeout(1000);
						
						// Take screenshot with guidance closed
						await page.screenshot({ path: 'test-results/guidance-closed.png', fullPage: true });
						
						// Check that guidance panel is gone
						const guidancePanelAfterClose = await guidancePanel.count();
						console.log(`Guidance panels after close: ${guidancePanelAfterClose}`);
						
						if (guidancePanelAfterClose === 0) {
							console.log('✅ Guidance panel closed successfully!');
						} else {
							console.log('❌ Guidance panel did not close');
						}
						
						// Try to open guidance again by clicking guidance button
						await guidanceButton.first().click();
						await page.waitForTimeout(1000);
						
						const guidancePanelAfterReopen = await guidancePanel.count();
						console.log(`Guidance panels after reopen: ${guidancePanelAfterReopen}`);
						
						if (guidancePanelAfterReopen > 0) {
							console.log('✅ Guidance can be reopened!');
						} else {
							console.log('❌ Guidance cannot be reopened');
						}
						
						// Take final screenshot
						await page.screenshot({ path: 'test-results/guidance-reopened.png', fullPage: true });
						
					} else {
						console.log('❌ Close button not found');
					}
				} else {
					console.log('❌ Guidance panel not found after clicking button');
				}
			} else {
				console.log('❌ Guidance button not found');
			}
		} else {
			console.log('❌ No collections found');
		}
	});
});
