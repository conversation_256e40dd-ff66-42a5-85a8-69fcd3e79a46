import { test, expect } from '@playwright/test';

test.describe('Debug Guidance Issue', () => {
	test('should check if guidance appears on collections pages', async ({ page }) => {
		// Navigate to localhost:3001 (our dev server)
		await page.goto('http://localhost:3001', { 
			waitUntil: 'networkidle',
			timeout: 15000 
		});
		
		await page.waitForTimeout(3000);
		
		// Take screenshot of homepage
		await page.screenshot({ path: 'test-results/homepage.png', fullPage: true });
		
		// Try to find collections link and navigate
		const collectionsLink = page.locator('a[href*="/collections"]').first();
		if (await collectionsLink.count() > 0) {
			await collectionsLink.click();
			await page.waitForTimeout(2000);
			
			// Take screenshot of collections page
			await page.screenshot({ path: 'test-results/collections-page.png', fullPage: true });
			
			// Look for any collection to click
			const collectionCard = page.locator('[data-testid*="collection"], .collection-card, a[href*="/collections/"]').first();
			if (await collectionCard.count() > 0) {
				await collectionCard.click();
				await page.waitForTimeout(2000);
				
				// Take screenshot of collection overview
				await page.screenshot({ path: 'test-results/collection-overview.png', fullPage: true });
				
				// Try to navigate to vocabulary/my-words
				const myWordsLink = page.locator('a[href*="/vocabulary/my-words"], a[href*="/my-words"]').first();
				if (await myWordsLink.count() > 0) {
					await myWordsLink.click();
					await page.waitForTimeout(3000);
					
					// Take screenshot of my-words page
					await page.screenshot({ path: 'test-results/my-words-page.png', fullPage: true });
					
					// Check for guidance elements
					await checkGuidanceElements(page, 'my-words');
				}
				
				// Try to navigate to vocabulary/generate
				const generateLink = page.locator('a[href*="/vocabulary/generate"], a[href*="/generate"]').first();
				if (await generateLink.count() > 0) {
					await generateLink.click();
					await page.waitForTimeout(3000);
					
					// Take screenshot of generate page
					await page.screenshot({ path: 'test-results/generate-page.png', fullPage: true });
					
					// Check for guidance elements
					await checkGuidanceElements(page, 'generate');
				}
			}
		}
	});
});

async function checkGuidanceElements(page: any, pageName: string) {
	console.log(`\n🔍 Checking guidance elements on ${pageName} page:`);
	
	// Check for DOM floating portal
	const domFloatingPortal = page.locator('#dom-floating-portal');
	const hasPortal = await domFloatingPortal.count() > 0;
	console.log(`   DOM Floating Portal exists: ${hasPortal}`);
	
	if (hasPortal) {
		const portalContent = await domFloatingPortal.innerHTML();
		console.log(`   Portal content length: ${portalContent.length}`);
		console.log(`   Portal content preview: ${portalContent.substring(0, 200)}...`);
	}
	
	// Check for guidance buttons (HelpCircle icon)
	const guidanceButtons = page.locator('button').filter({ 
		has: page.locator('svg[class*="lucide-help-circle"]')
	});
	const guidanceButtonCount = await guidanceButtons.count();
	console.log(`   Guidance buttons found: ${guidanceButtonCount}`);
	
	// Check for any floating elements
	const floatingElements = page.locator('[style*="position: fixed"]');
	const floatingCount = await floatingElements.count();
	console.log(`   Fixed position elements: ${floatingCount}`);
	
	// Check for guidance-related elements
	const guidanceElements = page.locator('[id*="guidance"], [class*="guidance"]');
	const guidanceElementCount = await guidanceElements.count();
	console.log(`   Guidance-related elements: ${guidanceElementCount}`);
	
	// Check for PageGuidance component specifically
	const pageGuidanceElements = page.locator('[id*="page-guidance"], [id*="my-words-guidance"], [id*="generate-words-guidance"]');
	const pageGuidanceCount = await pageGuidanceElements.count();
	console.log(`   PageGuidance elements: ${pageGuidanceCount}`);
	
	// Check console errors
	const consoleErrors: string[] = [];
	page.on('console', (msg) => {
		if (msg.type() === 'error') {
			consoleErrors.push(msg.text());
		}
	});
	
	if (consoleErrors.length > 0) {
		console.log(`   Console errors: ${consoleErrors.length}`);
		consoleErrors.forEach((error, i) => {
			console.log(`     ${i + 1}. ${error}`);
		});
	}
}
