'use client';

import { useEffect } from 'react';
import { useGuidance } from '@/contexts';

interface GuidanceStep {
	key: string;
	icon?: React.ComponentType<any>;
}

interface UsePageGuidanceOptions {
	titleKey: string;
	steps: GuidanceStep[];
	tipKey?: string;
	requirementKey?: string;
	defaultOpen?: boolean;
	autoShow?: boolean;
}

export function usePageGuidance(options: UsePageGuidanceOptions) {
	const { showGuidance, hideGuidance, isOpen, config } = useGuidance();

	useEffect(() => {
		// Register guidance config
		showGuidance({
			titleKey: options.titleKey,
			steps: options.steps,
			tipKey: options.tipKey,
			requirementKey: options.requirementKey,
			defaultOpen: options.defaultOpen || false,
		});

		// Cleanup when component unmounts
		return () => {
			hideGuidance();
		};
	}, [
		options.titleKey,
		options.steps,
		options.tipKey,
		options.requirementKey,
		options.defaultOpen,
		showGuidance,
		hideGuidance,
	]);

	return {
		isOpen,
		config,
		showGuidance: () =>
			showGuidance({
				titleKey: options.titleKey,
				steps: options.steps,
				tipKey: options.tipKey,
				requirementKey: options.requirementKey,
				defaultOpen: options.defaultOpen,
			}),
		hideGuidance,
	};
}
