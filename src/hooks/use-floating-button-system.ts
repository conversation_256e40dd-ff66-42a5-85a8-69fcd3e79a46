'use client';

import React, { useCallback, useMemo, useState } from 'react';
import { useFloatingUI } from '@/contexts/floating-ui-context';

// ============================================================================
// TYPES
// ============================================================================

export interface FloatingButtonItem {
	id: string;
	label: string;
	icon: React.ReactNode;
	onClick: () => void;
	priority?: number;
	visible?: boolean;
	disabled?: boolean;
	className?: string;
}

export interface FloatingButtonSystemOptions {
	position?: 'bottom-right' | 'bottom-left' | 'top-right' | 'top-left';
	gap?: number;
	maxVisible?: number;
	autoHide?: boolean;
	collisionDetection?: boolean;
}

// ============================================================================
// FLOATING BUTTON SYSTEM HOOK
// ============================================================================

export function useFloatingButtonSystem(
	systemId: string,
	options: FloatingButtonSystemOptions = {}
) {
	const {
		position = 'bottom-right',
		gap = 2,
		maxVisible = 10,
		autoHide: _autoHide = false,
		collisionDetection: _collisionDetection = true,
	} = options;

	const { state: _state, update: _update } = useFloatingUI();
	const [buttons, setButtons] = useState<Map<string, FloatingButtonItem>>(new Map());

	// Get position coordinates based on position
	const coordinates = useMemo(() => {
		const positions = {
			'bottom-right': { bottom: 24, right: 24 },
			'bottom-left': { bottom: 24, left: 24 },
			'top-right': { top: 24, right: 24 },
			'top-left': { top: 24, left: 24 },
		};
		return positions[position];
	}, [position]);

	// Get sorted and filtered buttons
	const visibleButtons = useMemo(() => {
		return Array.from(buttons.values())
			.filter((button) => button.visible !== false)
			.sort((a, b) => (b.priority || 0) - (a.priority || 0))
			.slice(0, maxVisible);
	}, [buttons, maxVisible]);

	// Register a button
	const registerButton = useCallback((button: FloatingButtonItem) => {
		setButtons((prev) => {
			const newButtons = new Map(prev);
			newButtons.set(button.id, { ...button, visible: button.visible !== false });
			return newButtons;
		});
	}, []);

	// Unregister a button
	const unregisterButton = useCallback((buttonId: string) => {
		setButtons((prev) => {
			const newButtons = new Map(prev);
			newButtons.delete(buttonId);
			return newButtons;
		});
	}, []);

	// Update a button
	const updateButton = useCallback((buttonId: string, updates: Partial<FloatingButtonItem>) => {
		setButtons((prev) => {
			const newButtons = new Map(prev);
			const existingButton = newButtons.get(buttonId);
			if (existingButton) {
				newButtons.set(buttonId, { ...existingButton, ...updates });
			}
			return newButtons;
		});
	}, []);

	// Show a button
	const showButton = useCallback(
		(buttonId: string) => {
			updateButton(buttonId, { visible: true });
		},
		[updateButton]
	);

	// Hide a button
	const hideButton = useCallback(
		(buttonId: string) => {
			updateButton(buttonId, { visible: false });
		},
		[updateButton]
	);

	// Toggle a button
	const toggleButton = useCallback(
		(buttonId: string) => {
			setButtons((prev) => {
				const button = prev.get(buttonId);
				if (button) {
					updateButton(buttonId, { visible: !button.visible });
				}
				return prev;
			});
		},
		[updateButton]
	);

	// Hide all buttons
	const hideAllButtons = useCallback(() => {
		setButtons((prev) => {
			const newButtons = new Map();
			prev.forEach((button, id) => {
				newButtons.set(id, { ...button, visible: false });
			});
			return newButtons;
		});
	}, []);

	// Show all buttons
	const showAllButtons = useCallback(() => {
		setButtons((prev) => {
			const newButtons = new Map();
			prev.forEach((button, id) => {
				newButtons.set(id, { ...button, visible: true });
			});
			return newButtons;
		});
	}, []);

	// Get button by id
	const getButton = useCallback(
		(buttonId: string) => {
			return buttons.get(buttonId);
		},
		[buttons]
	);

	// Get all buttons
	const getAllButtons = useCallback(() => {
		return Array.from(buttons.values());
	}, [buttons]);

	// Calculate positions for buttons in vertical stack
	const calculateButtonPositions = useCallback(() => {
		const buttonHeight = 56; // 14 * 4 = 56px (h-14)
		const gapSize = gap * 4; // Convert to pixels

		return visibleButtons.map((button, index) => {
			const offset = index * (buttonHeight + gapSize);

			if (position.startsWith('bottom')) {
				return {
					...coordinates,
					bottom: (coordinates.bottom || 24) + offset,
				};
			} else {
				return {
					...coordinates,
					top: (coordinates.top || 24) + offset,
				};
			}
		});
	}, [visibleButtons, coordinates, gap, position]);

	// Get system state
	const systemState = useMemo(
		() => ({
			buttons: visibleButtons,
			positions: calculateButtonPositions(),
			totalButtons: buttons.size,
			visibleCount: visibleButtons.length,
			coordinates,
			position,
			gap,
		}),
		[visibleButtons, calculateButtonPositions, buttons.size, coordinates, position, gap]
	);

	return {
		// State
		buttons: visibleButtons,
		allButtons: getAllButtons(),
		systemState,

		// Actions
		registerButton,
		unregisterButton,
		updateButton,
		showButton,
		hideButton,
		toggleButton,
		hideAllButtons,
		showAllButtons,
		getButton,

		// Utilities
		coordinates,
		calculateButtonPositions,
	};
}

// ============================================================================
// INDIVIDUAL FLOATING BUTTON HOOK
// ============================================================================

export function useFloatingButton(
	buttonId: string,
	button: Omit<FloatingButtonItem, 'id'>,
	systemId: string = 'default'
) {
	const system = useFloatingButtonSystem(systemId);

	// Register button on mount
	React.useEffect(() => {
		system.registerButton({ ...button, id: buttonId });

		return () => {
			system.unregisterButton(buttonId);
		};
	}, [buttonId, system.registerButton, system.unregisterButton]);

	// Update button when props change
	React.useEffect(() => {
		system.updateButton(buttonId, button);
	}, [buttonId, button, system.updateButton]);

	const currentButton = system.getButton(buttonId);

	return {
		button: currentButton,
		show: () => system.showButton(buttonId),
		hide: () => system.hideButton(buttonId),
		toggle: () => system.toggleButton(buttonId),
		update: (updates: Partial<FloatingButtonItem>) => system.updateButton(buttonId, updates),
		isVisible: currentButton?.visible || false,
	};
}
