'use client';

import React, { createContext, useContext, useState, ReactNode } from 'react';

interface GuidanceStep {
	key: string;
	icon?: React.ComponentType<any>;
}

interface GuidanceConfig {
	titleKey: string;
	steps: GuidanceStep[];
	tipKey?: string;
	requirementKey?: string;
	defaultOpen?: boolean;
}

interface GuidanceContextType {
	isOpen: boolean;
	config: GuidanceConfig | null;
	showGuidance: (config: GuidanceConfig) => void;
	hideGuidance: () => void;
	toggleGuidance: () => void;
}

const GuidanceContext = createContext<GuidanceContextType | undefined>(undefined);

export function GuidanceProvider({ children }: { children: ReactNode }) {
	const [isOpen, setIsOpen] = useState(false);
	const [config, setConfig] = useState<GuidanceConfig | null>(null);

	const showGuidance = (newConfig: GuidanceConfig) => {
		setConfig(newConfig);
		// Always set isOpen based on defaultOpen, but allow manual toggle later
		setIsOpen(newConfig.defaultOpen || false);
	};

	const hideGuidance = () => {
		setIsOpen(false);
	};

	const toggleGuidance = () => {
		if (config) {
			setIsOpen(!isOpen);
		}
	};

	return (
		<GuidanceContext.Provider
			value={{
				isOpen,
				config,
				showGuidance,
				hideGuidance,
				toggleGuidance,
			}}
		>
			{children}
		</GuidanceContext.Provider>
	);
}

export function useGuidance() {
	const context = useContext(GuidanceContext);
	if (context === undefined) {
		throw new Error('useGuidance must be used within a GuidanceProvider');
	}
	return context;
}
