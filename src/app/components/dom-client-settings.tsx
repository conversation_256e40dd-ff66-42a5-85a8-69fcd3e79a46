'use client';

import { useFloatingUIElement } from '@/hooks/use-floating-ui';
import { useState } from 'react';
import { EnhancedSettingsPanel } from '@/components/settings/enhanced-settings-panel';

export function DOMClientSettings() {
	const [isOpen, setIsOpen] = useState(false);

	const handleToggle = () => setIsOpen(!isOpen);
	const handleClose = () => setIsOpen(false);

	// Use floating UI element for positioning
	const {
		show: _show,
		hide: _hide,
		toggle: _toggle,
		isVisible: _isVisible,
	} = useFloatingUIElement(
		'unified-settings',
		<EnhancedSettingsPanel isOpen={isOpen} onClose={handleClose} onToggle={handleToggle} />,
		{
			type: 'custom',
			priority: 'high',
			position: 'bottom-right',
			coordinates: { bottom: 16, right: 16 },
			animation: { type: 'scale', duration: 200 },
			autoShow: true,
			collisionDetection: true,
			className: 'floating-settings-element',
		}
	);

	return null; // Content is rendered through DOM floating system
}
