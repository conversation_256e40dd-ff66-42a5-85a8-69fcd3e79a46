'use client';

import { SimplePageGuidance, SuccessBanner } from '@/components/onboarding';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Translate, useToast } from '@/components/ui';
import { useKeywordsContext, useTranslation } from '@/contexts';
import { useLLM } from '@/contexts/llm-context';
import { useCollections } from '@/hooks';
import { createWordAddedAction, useUndoActions } from '@/hooks/use-undo-actions';
import { RandomWord, WordDetail } from '@/models';
import { Sparkles } from 'lucide-react';
import { useEffect, useState } from 'react';
import { KeywordForm } from '../../components/keyword-form';
import { PracticeSessionSkeleton } from '../../components/practice-session-skeleton';
import { WordList } from './components/word-list';

// API client function for paginated word generation
async function generateWordsPaginated(
	collectionId: string,
	keywords: string[],
	maxTerms: number,
	excludeTerms: string[],
	sourceLanguage: string,
	targetLanguage: string,
	offset: number = 0
) {
	const response = await fetch(`/api/collections/${collectionId}/vocabulary/generate`, {
		method: 'POST',
		headers: {
			'Content-Type': 'application/json',
		},
		body: JSON.stringify({
			keywords,
			maxTerms,
			excludeTerms,
			sourceLanguage,
			targetLanguage,
			offset,
		}),
	});

	if (!response.ok) {
		const error = await response.json();
		throw new Error(error.error || 'Failed to generate words');
	}

	return response.json();
}

export function GenerateWordsClient() {
	const { t } = useTranslation();
	const { toast } = useToast();
	const { generateWordDetails } = useLLM();
	const {
		currentCollection,
		loading: collectionsLoading,
		error: collectionsError,
		addTermToCurrentCollection,
		addWordsToCurrentCollection,
		removeWordsFromCurrentCollection,
		refreshCurrentCollection,
	} = useCollections();
	const {
		isLoading: isKeywordsLoading,
		selectedKeywords,
		keywords,
		error: keywordsError,
	} = useKeywordsContext();

	// Undo actions hook
	const { actions: undoActions, addUndoAction, executeUndo } = useUndoActions(5); // Keep last 5 actions

	// Simple state management
	const [generatedWords, setGeneratedWords] = useState<RandomWord[]>([]);
	const [detailedWords, setDetailedWords] = useState<Record<string, WordDetail>>({});
	const [isGenerating, setIsGenerating] = useState(false);
	const [isLoadingMore, setIsLoadingMore] = useState(false);
	const [currentOffset, setCurrentOffset] = useState(0);
	const [currentKeywords, setCurrentKeywords] = useState<string[]>([]);
	const [wordLoadingStates, setWordLoadingStates] = useState<
		Record<string, { adding: boolean; gettingDetail: boolean }>
	>({});

	// State to track which words have been added (for showing undo button)
	const [addedWords, setAddedWords] = useState<Set<string>>(new Set());

	// Success tracking
	const [showSuccessBanner, setShowSuccessBanner] = useState(false);
	const [sessionAddedCount, setSessionAddedCount] = useState(0);
	const [initialWordCount, setInitialWordCount] = useState(0);

	// Check if any critical loading is happening
	const isCollectionsLoading = collectionsLoading.get || collectionsLoading.setCurrent;
	const isAnyLoading = isCollectionsLoading || isGenerating || isKeywordsLoading;
	const hasErrors = !!(collectionsError || keywordsError);

	// Initialize word count tracking
	useEffect(() => {
		if (currentCollection && initialWordCount === 0) {
			setInitialWordCount(currentCollection.word_ids.length);
		}
	}, [currentCollection, initialWordCount]);

	// Error notifications
	useEffect(() => {
		if (collectionsError) {
			toast({
				variant: 'destructive',
				title: t('collections.error'),
				description: collectionsError.message,
			});
		}
		if (keywordsError) {
			toast({
				variant: 'destructive',
				title: t('keywords.error'),
				description: keywordsError.message,
			});
		}
	}, [collectionsError, keywordsError, t, toast]);

	// Collection safety check
	if (!currentCollection) return null;

	// Generate words function (initial load)
	const generateWords = async (keywords: string[]) => {
		setIsGenerating(true);
		try {
			const result = await generateWordsPaginated(
				currentCollection.id,
				keywords,
				20, // Initial load: 20 words
				[], // No excluded terms for initial load
				currentCollection.source_language,
				currentCollection.target_language,
				0 // Start from offset 0
			);

			setGeneratedWords(result);
			setCurrentOffset(result.length);
			setCurrentKeywords(keywords);
		} catch (error) {
			const err = error instanceof Error ? error : new Error('Failed to generate words');
			toast({
				variant: 'destructive',
				title: t('words.generation_failed'),
				description: err.message,
			});
		} finally {
			setIsGenerating(false);
		}
	};

	// Load more words function
	const loadMoreWords = async () => {
		if (isLoadingMore || currentKeywords.length === 0) return;

		setIsLoadingMore(true);
		try {
			const excludeTerms = generatedWords.map((word) => word.term);
			const result = await generateWordsPaginated(
				currentCollection.id,
				currentKeywords,
				20, // Load 20 more words
				excludeTerms, // Exclude already loaded words
				currentCollection.source_language,
				currentCollection.target_language,
				currentOffset
			);

			// Append new words to existing list
			setGeneratedWords((prev) => [...prev, ...result]);
			setCurrentOffset((prev) => prev + result.length);
		} catch (error) {
			const err = error instanceof Error ? error : new Error('Failed to load more words');
			toast({
				variant: 'destructive',
				title: t('words.load_more_failed'),
				description: err.message,
			});
		} finally {
			setIsLoadingMore(false);
		}
	};

	// Get word details
	const handleGetDetails = async (word: RandomWord) => {
		if (wordLoadingStates[word.term]?.gettingDetail || detailedWords[word.term]) return;

		setWordLoadingStates((prev) => ({
			...prev,
			[word.term]: { ...prev[word.term], gettingDetail: true },
		}));

		try {
			const detailsList = await generateWordDetails(
				[word.term],
				currentCollection.source_language,
				currentCollection.target_language
			);
			if (detailsList && detailsList.length > 0) {
				const wordDetail = detailsList[0] as WordDetail;
				setDetailedWords((prev) => ({ ...prev, [word.term]: wordDetail }));
			} else {
				throw new Error(t('words.detail_fetch_no_data', { term: word.term }));
			}
		} catch (error) {
			const err = error instanceof Error ? error : new Error(String(error));
			toast({
				variant: 'destructive',
				title: t('words.detail_fetch_error'),
				description: err.message,
			});
		} finally {
			setWordLoadingStates((prev) => ({
				...prev,
				[word.term]: { ...prev[word.term], gettingDetail: false },
			}));
		}
	};

	// Add word to collection
	const handleAddToCollection = async (word: RandomWord) => {
		if (wordLoadingStates[word.term]?.adding) return;

		setWordLoadingStates((prev) => ({
			...prev,
			[word.term]: { ...prev[word.term], adding: true },
		}));

		try {
			let addedWordId: string | undefined;

			if (detailedWords[word.term]?.id) {
				await addWordsToCurrentCollection([detailedWords[word.term].id]);
				addedWordId = detailedWords[word.term].id;
			} else {
				const result = await addTermToCurrentCollection(
					word.term,
					currentCollection.target_language
				);
				// Find the newly added word ID by looking for the word with matching term
				if (result) {
					const addedWord = result.words.find((w) => w.term === word.term);
					addedWordId = addedWord?.id;
				}
			}

			// Create undo action
			if (addedWordId) {
				const undoAction = createWordAddedAction(
					{ term: word.term, id: addedWordId },
					currentCollection.id,
					async (wordId: string) => {
						await removeWordsFromCurrentCollection([wordId]);
						await refreshCurrentCollection();
						// Remove from added words set when undoing
						setAddedWords((prev) => {
							const newSet = new Set(prev);
							newSet.delete(word.term);
							return newSet;
						});
						// Update session tracking
						setSessionAddedCount((prev) => Math.max(0, prev - 1));
					}
				);
				addUndoAction(undoAction);
			}

			// Mark word as added (for UI state)
			setAddedWords((prev) => new Set(prev).add(word.term));

			// Update session tracking
			setSessionAddedCount((prev) => prev + 1);
			setShowSuccessBanner(true);

			// Show success toast
			toast({
				title: t('words.word_added'),
				description: t('words.word_added_desc', { term: word.term }),
			});

			await refreshCurrentCollection();
			setWordLoadingStates((prev) => {
				const { [word.term]: _, ...rest } = prev;
				return rest;
			});
		} catch (error) {
			const err = error instanceof Error ? error : new Error(String(error));
			toast({
				variant: 'destructive',
				title: t('words.add_error'),
				description: t('words.add_error_desc', {
					term: word.term,
					message: err.message,
				}),
			});
		} finally {
			setWordLoadingStates((prev) => ({
				...prev,
				[word.term]: { ...prev[word.term], adding: false },
			}));
		}
	};

	// Show loading skeleton while any critical data is loading
	if (isAnyLoading && !currentCollection) return <PracticeSessionSkeleton type="paragraph" />;

	// Handle generate button click
	const handleGenerate = async () => {
		if (selectedKeywords.length === 0 || isAnyLoading || hasErrors) return;

		const keywordNames = selectedKeywords
			.map((id) => {
				const keyword = keywords.find((k) => k.id === id);
				return keyword?.content || '';
			})
			.filter(Boolean);

		if (keywordNames.length === 0) {
			toast({
				variant: 'destructive',
				title: t('keywords.error'),
				description: t('keywords.no_valid_selected'),
			});
			return;
		}

		await generateWords(keywordNames);
	};

	// Helper function to get word loading state
	const getWordLoadingState = (term: string) => {
		return wordLoadingStates[term] || { adding: false, gettingDetail: false };
	};

	// Handle undo action for a specific word
	const handleUndoAddWord = async (word: RandomWord) => {
		// Find the undo action for this word
		const wordAction = undoActions.find(
			(action) => action.type === 'word_added' && action.data.word.term === word.term
		);

		if (wordAction) {
			try {
				const success = await executeUndo(wordAction.id);

				if (success) {
					// Show success toast for undo action
					toast({
						title: t('words.word_removed'),
						description: t('words.word_removed_desc', { term: word.term }),
					});
				} else {
					toast({
						variant: 'destructive',
						title: t('words.undo_error'),
						description: 'Undo operation failed',
					});
				}
			} catch (error) {
				console.error('Error during undo:', error);
				const err = error instanceof Error ? error : new Error(String(error));
				toast({
					variant: 'destructive',
					title: t('words.undo_error'),
					description: t('words.undo_error_desc', {
						term: word.term,
						message: err.message,
					}),
				});
			}
		} else {
			console.warn('No undo action found for word:', word.term);
			toast({
				variant: 'destructive',
				title: t('words.undo_error'),
				description: 'No undo action available for this word',
			});
		}
	};

	const currentWordCount = currentCollection.word_ids.length;

	return (
		<>
			<div className="min-h-screen bg-gradient-to-br from-background to-blue-50 dark:from-background dark:to-background">
				<div className="max-w-6xl mx-auto space-y-8">
					<header className="text-center space-y-4">
						<h1 className="text-4xl font-bold text-primary dark:text-primary">
							<Translate text="words.generate_words" />
						</h1>
						<p className="text-muted-foreground dark:text-muted-foreground text-lg">
							<Translate text="words.generate_description" />
						</p>
					</header>

					{/* Success Banner */}
					{showSuccessBanner && sessionAddedCount > 0 && (
						<SuccessBanner
							collectionId={currentCollection.id}
							addedWordsCount={sessionAddedCount}
							totalWordsInCollection={currentWordCount}
							onDismiss={() => setShowSuccessBanner(false)}
						/>
					)}

					{/* Page Guidance */}
					<SimplePageGuidance
						id="generate-words-guidance"
						titleKey="words.guidance.generate.title"
						steps={[
							{ key: 'words.guidance.generate.step1' },
							{ key: 'words.guidance.generate.step2' },
							{ key: 'words.guidance.generate.step3' },
						]}
						tipKey="words.guidance.generate.tip"
						defaultOpen={currentWordCount === 0}
					/>

					<section className="space-y-4">
						<KeywordForm />

						{/* Generate button */}
						<div className="space-y-3">
							{selectedKeywords.length > 0 && (
								<div className="text-sm text-primary/80 font-medium">
									<Translate
										text="words.selected_count"
										values={{ count: selectedKeywords.length }}
									/>
								</div>
							)}
							<Button
								className="w-full h-12 text-base font-bold rounded-2xl bg-primary text-background shadow-2xl hover:bg-primary/90 transition-all duration-200 flex gap-3 items-center justify-center"
								disabled={
									selectedKeywords.length === 0 || isAnyLoading || hasErrors
								}
								onClick={handleGenerate}
								size="sm"
								loading={isGenerating || isKeywordsLoading}
							>
								{isGenerating || isKeywordsLoading ? (
									<>
										<LoadingSpinner size="sm" />
										<Translate
											text={isGenerating ? 'words.generating' : 'ui.loading'}
										/>
									</>
								) : (
									<>
										<Sparkles className="h-6 w-6" />
										<Translate text="words.generate_words" />
									</>
								)}
							</Button>
						</div>

						<div className="grid grid-cols-1 gap-4">
							{/* Show loading state for word list operations */}
							{isGenerating ? (
								<div className="flex items-center justify-center p-8 border-2 border-dashed border-muted-foreground/20 rounded-lg">
									<div className="flex flex-col items-center space-y-3">
										<LoadingSpinner size="lg" />
										<p className="text-muted-foreground">
											<Translate text="words.generating_please_wait" />
										</p>
									</div>
								</div>
							) : (
								<div className={`relative`}>
									<WordList
										words={generatedWords}
										detailedWords={detailedWords}
										onGetDetails={handleGetDetails}
										getLoadingState={getWordLoadingState}
										onAddToCollection={handleAddToCollection}
										onUndoAddWord={handleUndoAddWord}
										addedWords={addedWords}
										className="mt-6"
										sourceLanguage={currentCollection.source_language}
										targetLanguage={currentCollection.target_language}
										isLoadingMore={isLoadingMore}
										onLoadMore={loadMoreWords}
									/>
								</div>
							)}
						</div>
					</section>
				</div>
			</div>
		</>
	);
}
