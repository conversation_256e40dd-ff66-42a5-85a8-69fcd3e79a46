'use client';

import {
	But<PERSON>,
	Label,
	LoadingSpinner,
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
	Translate,
} from '@/components/ui';
import { useTranslation } from '@/contexts';
import { useCollections } from '@/hooks';
import { Difficulty } from '@prisma/client';
import { AlertCircle, Play, Settings } from 'lucide-react';
import { KeywordForm } from '../../components/keyword-form';
import { GenerationLoadingState, ParagraphPracticeSkeleton } from '../components';
import { ErrorTypesGuidance } from './error-types-guidance';
import { GrammarPracticeItem } from './grammar-practice-item';
import type { ErrorDensity } from './types';
import { useGrammarPractice } from './use-grammar-practice';

export function GrammarPracticeClient() {
	const { t } = useTranslation();
	const { currentCollection: collection, loading } = useCollections();

	const {
		difficulty,
		setDifficulty,
		isLoading,
		errorDensity,
		generateParagraphs,
		setErrorDensity,
		selectedKeywords,
		hasError,
		errorMessage,
		paragraphs,
		selections,
		toggleWordSelection,
	} = useGrammarPractice(collection);

	// Show skeleton while collection is loading, initial keywords are loading, or collection doesn't exist
	if (loading.get || loading.setCurrent || !collection) {
		return <ParagraphPracticeSkeleton />;
	}

	return (
		<div className="min-h-screen bg-gradient-to-br from-background to-blue-50 dark:from-background dark:to-background">
			<div className="max-w-6xl mx-auto space-y-8">
				<header className="text-center space-y-4">
					<h1 className="text-4xl font-bold text-primary dark:text-primary">
						<Translate text="grammar.practice_title" />
					</h1>
					<p className="text-muted-foreground dark:text-muted-foreground text-lg">
						<Translate text="grammar.practice_description" />
					</p>
				</header>

				<div className="space-y-6">
					<div className="flex items-center gap-3 mb-6">
						<Settings className="w-6 h-6 text-blue-600 dark:text-blue-400" />
						<h2 className="text-xl font-semibold text-primary dark:text-primary">
							Practice Configuration
						</h2>
					</div>

					<KeywordForm />

					<div className="grid grid-cols-1 md:grid-cols-2 gap-6">
						<div className="space-y-3">
							<Label className="text-sm font-semibold text-primary dark:text-primary">
								<Translate text="difficulty.select_difficulty" />
							</Label>
							<Select
								value={difficulty as string}
								onValueChange={(value) => setDifficulty(value as Difficulty)}
								disabled={isLoading}
							>
								<SelectTrigger className="w-full h-12 border-2 border-border dark:border-border rounded-lg">
									<SelectValue placeholder={t('difficulty.select_difficulty')} />
								</SelectTrigger>
								<SelectContent>
									<SelectItem value={Difficulty.BEGINNER}>
										{t('difficulty.BEGINNER')}
									</SelectItem>
									<SelectItem value={Difficulty.INTERMEDIATE}>
										{t('difficulty.INTERMEDIATE')}
									</SelectItem>
									<SelectItem value={Difficulty.ADVANCED}>
										{t('difficulty.ADVANCED')}
									</SelectItem>
								</SelectContent>
							</Select>
						</div>
						<div className="space-y-3">
							<Label className="text-sm font-semibold text-primary dark:text-primary">
								<Translate text="grammar.error_density.label" />
							</Label>
							<Select
								value={errorDensity}
								onValueChange={(value) => setErrorDensity(value as ErrorDensity)}
								disabled={isLoading}
							>
								<SelectTrigger className="w-full h-12 border-2 border-border dark:border-border rounded-lg">
									<SelectValue placeholder={t('grammar.error_density.label')} />
								</SelectTrigger>
								<SelectContent>
									<SelectItem value="low">
										{t('grammar.error_density.low')}
									</SelectItem>
									<SelectItem value="medium">
										{t('grammar.error_density.medium')}
									</SelectItem>
									<SelectItem value="high">
										{t('grammar.error_density.high')}
									</SelectItem>
								</SelectContent>
							</Select>
						</div>
					</div>

					<Button
						onClick={generateParagraphs}
						disabled={isLoading || !selectedKeywords.length}
						size="lg"
						className="w-full sm:w-auto h-12 px-8 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105"
					>
						{isLoading ? (
							<>
								<LoadingSpinner size="sm" />
								<span className="ml-3">
									<Translate text="grammar.generating" />
								</span>
							</>
						) : (
							<>
								<Play className="w-5 h-5 mr-2" />
								<Translate text="grammar.view_result_button" />
							</>
						)}
					</Button>
				</div>

				{hasError && (
					<div className="bg-gradient-to-r from-red-50 to-pink-50 dark:from-red-950/30 dark:to-pink-950/30 border-2 border-red-200 dark:border-red-800 rounded-xl p-6 shadow-sm">
						<div className="flex items-center gap-3">
							<div className="w-10 h-10 bg-red-100 dark:bg-red-900/50 rounded-full flex items-center justify-center">
								<AlertCircle className="w-5 h-5 text-red-600 dark:text-red-400" />
							</div>
							<div>
								<span className="text-red-700 dark:text-red-300 font-semibold text-lg">
									<Translate text="grammar.generation_failed_title" />
								</span>
								<p className="text-red-600 dark:text-red-400 text-sm mt-1">
									{errorMessage}
								</p>
							</div>
						</div>
					</div>
				)}

				{isLoading && (
					<GenerationLoadingState
						titleKey="grammar.generating_paragraphs"
						description="Creating personalized grammar exercises..."
					/>
				)}

				{!isLoading && paragraphs.length > 0 && (
					<section className="space-y-8">
						<div className="space-y-6">
							{paragraphs.map((item, index) => (
								<GrammarPracticeItem
									key={index}
									item={item}
									index={index}
									selectedWords={selections[index] || []}
									onWordSelection={toggleWordSelection}
									targetLanguage={collection.target_language}
									sourceLanguage={collection.source_language}
								/>
							))}
						</div>
					</section>
				)}
			</div>

			{/* Error Types Guidance */}
			<ErrorTypesGuidance id="grammar-practice-error-types" />
		</div>
	);
}
