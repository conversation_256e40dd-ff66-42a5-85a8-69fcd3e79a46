'use client';

import { <PERSON><PERSON>, <PERSON>, Separator, Switch, useTheme } from '@/components/ui';
import { useTranslation } from '@/contexts';
import { useAuth } from '@/contexts/auth-context';
import { getTranslationKeyOfLanguage } from '@/contexts/translations';
import { Language } from '@prisma/client';
import { motion, AnimatePresence } from 'framer-motion';
import { Languages, LogOut, Monitor, Moon, Settings, Sun, User, X } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useState } from 'react';
import {
	settingsPanelVariants,
	settingsSectionVariants,
	settingsButtonVariants,
	settingsItemVariants,
	backdropVariants,
} from '@/components/animations/settings-animations';

interface EnhancedSettingsPanelProps {
	isOpen: boolean;
	onClose: () => void;
	onToggle: () => void;
}

export function EnhancedSettingsPanel({ isOpen, onClose, onToggle }: EnhancedSettingsPanelProps) {
	const { theme, setTheme } = useTheme();
	const { language, setLanguage, t } = useTranslation();
	const { logout, user } = useAuth();
	const router = useRouter();

	const handleLogout = async () => {
		try {
			await logout();
			router.push('/login');
		} catch (error) {
			console.error('Logout failed:', error);
		}
	};

	const handleThemeChange = (newTheme: 'light' | 'dark' | 'system') => {
		setTheme(newTheme);
	};

	const handleLanguageChange = (newLanguage: Language) => {
		setLanguage(newLanguage);
	};

	if (!isOpen) {
		return (
			<motion.div
				variants={settingsButtonVariants}
				initial="idle"
				whileHover="hover"
				whileTap="tap"
			>
				<Button
					size="icon"
					className="h-14 w-14 rounded-full shadow-lg hover:shadow-xl transition-shadow duration-200"
					onClick={onToggle}
					title={t('settings.button')}
					aria-label={t('settings.button')}
				>
					<Settings className="h-6 w-6" />
				</Button>
			</motion.div>
		);
	}

	return (
		<AnimatePresence>
			<motion.div
				className="fixed inset-0 z-[1300] flex items-center justify-center p-4"
				variants={backdropVariants}
				initial="hidden"
				animate="visible"
				exit="exit"
			>
				<motion.div
					className="absolute inset-0 bg-black/20 backdrop-blur-sm"
					onClick={onClose}
				/>
				<motion.div
					variants={settingsPanelVariants}
					initial="hidden"
					animate="visible"
					exit="exit"
					className="relative z-10"
				>
					<Card className="w-80 p-6 bg-background/95 backdrop-blur-sm border shadow-xl">
						{/* Header */}
						<motion.div
							variants={settingsSectionVariants}
							className="flex items-center justify-between mb-6"
						>
							<h3 className="text-xl font-semibold flex items-center gap-2">
								<Settings className="h-5 w-5 text-primary" />
								{t('settings.title')}
							</h3>
							<motion.div
								whileHover={{ scale: 1.1 }}
								whileTap={{ scale: 0.9 }}
							>
								<Button
									size="icon"
									variant="ghost"
									className="h-8 w-8"
									onClick={onClose}
								>
									<X className="h-4 w-4" />
								</Button>
							</motion.div>
						</motion.div>

						{/* User Section */}
						{user && (
							<motion.div variants={settingsSectionVariants} className="mb-6">
								<div className="flex items-center gap-2 mb-3">
									<User className="h-4 w-4 text-muted-foreground" />
									<span className="text-sm font-medium">{t('user.account')}</span>
								</div>
								<motion.div variants={settingsItemVariants} whileHover="hover">
									<Button
										variant="destructive"
										size="sm"
										className="w-full"
										onClick={handleLogout}
									>
										<LogOut className="h-4 w-4 mr-2" />
										{t('auth.logout')}
									</Button>
								</motion.div>
								<Separator className="mt-4" />
							</motion.div>
						)}

						{/* Language Section */}
						<motion.div variants={settingsSectionVariants} className="mb-6">
							<div className="flex items-center gap-2 mb-3">
								<Languages className="h-4 w-4 text-muted-foreground" />
								<span className="text-sm font-medium">{t('settings.language')}</span>
							</div>
							<div className="space-y-2">
								<motion.div variants={settingsItemVariants} whileHover="hover">
									<Button
										variant={language === Language.EN ? 'default' : 'outline'}
										size="sm"
										className="w-full justify-start"
										onClick={() => handleLanguageChange(Language.EN)}
									>
										{t(getTranslationKeyOfLanguage(Language.EN))}
									</Button>
								</motion.div>
								<motion.div variants={settingsItemVariants} whileHover="hover">
									<Button
										variant={language === Language.VI ? 'default' : 'outline'}
										size="sm"
										className="w-full justify-start"
										onClick={() => handleLanguageChange(Language.VI)}
									>
										{t(getTranslationKeyOfLanguage(Language.VI))}
									</Button>
								</motion.div>
							</div>
							<Separator className="mt-4" />
						</motion.div>

						{/* Theme Section */}
						<motion.div variants={settingsSectionVariants}>
							<div className="flex items-center gap-2 mb-3">
								<Monitor className="h-4 w-4 text-muted-foreground" />
								<span className="text-sm font-medium">{t('settings.theme')}</span>
							</div>
							<div className="space-y-2">
								<motion.div variants={settingsItemVariants} whileHover="hover">
									<Button
										variant={theme === 'light' ? 'default' : 'outline'}
										size="sm"
										className="w-full justify-start"
										onClick={() => handleThemeChange('light')}
									>
										<Sun className="h-4 w-4 mr-2" />
										{t('theme.light')}
									</Button>
								</motion.div>
								<motion.div variants={settingsItemVariants} whileHover="hover">
									<Button
										variant={theme === 'dark' ? 'default' : 'outline'}
										size="sm"
										className="w-full justify-start"
										onClick={() => handleThemeChange('dark')}
									>
										<Moon className="h-4 w-4 mr-2" />
										{t('theme.dark')}
									</Button>
								</motion.div>
								<motion.div variants={settingsItemVariants} whileHover="hover">
									<Button
										variant={theme === 'system' ? 'default' : 'outline'}
										size="sm"
										className="w-full justify-start"
										onClick={() => handleThemeChange('system')}
									>
										<Monitor className="h-4 w-4 mr-2" />
										{t('theme.system')}
									</Button>
								</motion.div>
							</div>
						</motion.div>
					</Card>
				</motion.div>
			</motion.div>
		</AnimatePresence>
	);
}
