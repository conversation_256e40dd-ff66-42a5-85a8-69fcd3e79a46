import { Variants } from 'framer-motion';

// Settings panel animations
export const settingsPanelVariants: Variants = {
	hidden: {
		opacity: 0,
		scale: 0.95,
		y: 10,
	},
	visible: {
		opacity: 1,
		scale: 1,
		y: 0,
		transition: {
			duration: 0.2,
			ease: 'easeOut',
			staggerChildren: 0.05,
		},
	},
	exit: {
		opacity: 0,
		scale: 0.95,
		y: 10,
		transition: {
			duration: 0.15,
			ease: 'easeIn',
		},
	},
};

// Settings section animations
export const settingsSectionVariants: Variants = {
	hidden: {
		opacity: 0,
		x: -10,
	},
	visible: {
		opacity: 1,
		x: 0,
		transition: {
			duration: 0.2,
			ease: 'easeOut',
		},
	},
};

// Settings button animations
export const settingsButtonVariants: Variants = {
	idle: {
		scale: 1,
		rotate: 0,
	},
	hover: {
		scale: 1.05,
		rotate: 90,
		transition: {
			duration: 0.2,
			ease: 'easeOut',
		},
	},
	tap: {
		scale: 0.95,
		transition: {
			duration: 0.1,
		},
	},
};

// Settings item animations
export const settingsItemVariants: Variants = {
	hidden: {
		opacity: 0,
		y: 5,
	},
	visible: {
		opacity: 1,
		y: 0,
		transition: {
			duration: 0.15,
			ease: 'easeOut',
		},
	},
	hover: {
		x: 2,
		transition: {
			duration: 0.1,
		},
	},
};

// Backdrop animations
export const backdropVariants: Variants = {
	hidden: {
		opacity: 0,
	},
	visible: {
		opacity: 1,
		transition: {
			duration: 0.2,
		},
	},
	exit: {
		opacity: 0,
		transition: {
			duration: 0.15,
		},
	},
};
