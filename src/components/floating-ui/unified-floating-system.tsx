'use client';

import React, { useEffect } from 'react';
import { Settings, MessageSquare } from 'lucide-react';
import { useFloatingButtonSystem } from '@/hooks/use-floating-button-system';
import { FloatingButtonManager } from './floating-button-manager';
import { useTranslation } from '@/contexts';

// ============================================================================
// UNIFIED FLOATING SYSTEM
// ============================================================================

interface UnifiedFloatingSystemProps {
	systemId?: string;
	position?: 'bottom-right' | 'bottom-left' | 'top-right' | 'top-left';
	gap?: number;
	includeSettings?: boolean;
	includeFeedback?: boolean;
	onSettingsClick?: () => void;
	onFeedbackClick?: () => void;
	className?: string;
}

export function UnifiedFloatingSystem({
	systemId = 'unified-system',
	position = 'bottom-right',
	gap = 2,
	includeSettings = true,
	includeFeedback = true,
	onSettingsClick,
	onFeedbackClick,
	className,
}: UnifiedFloatingSystemProps) {
	const { t } = useTranslation();
	const system = useFloatingButtonSystem(systemId, {
		position,
		gap,
		maxVisible: 10,
		collisionDetection: true,
	});

	// Register system buttons
	useEffect(() => {
		if (includeSettings) {
			system.registerButton({
				id: 'unified-settings',
				label: t('settings.button'),
				icon: <Settings className="h-6 w-6" />,
				onClick: onSettingsClick || (() => console.log('Settings clicked')),
				priority: 10, // Highest priority - closest to bottom
			});
		}

		if (includeFeedback) {
			system.registerButton({
				id: 'unified-feedback',
				label: t('feedback.button_title'),
				icon: <MessageSquare className="h-6 w-6" />,
				onClick: onFeedbackClick || (() => console.log('Feedback clicked')),
				priority: 8, // Second priority
			});
		}

		return () => {
			if (includeSettings) {
				system.unregisterButton('unified-settings');
			}
			if (includeFeedback) {
				system.unregisterButton('unified-feedback');
			}
		};
	}, [system, includeSettings, includeFeedback, onSettingsClick, onFeedbackClick, t]);

	return (
		<FloatingButtonManager
			systemId={systemId}
			position={position}
			gap={gap}
			maxVisible={10}
			collisionDetection={true}
			className={className}
		/>
	);
}

// ============================================================================
// FLOATING BUTTON REGISTRY
// ============================================================================

interface FloatingButtonRegistryProps {
	children?: React.ReactNode;
}

export function FloatingButtonRegistry({ children }: FloatingButtonRegistryProps) {
	return (
		<div className="floating-button-registry">
			{children}
			<UnifiedFloatingSystem />
		</div>
	);
}

// ============================================================================
// INDIVIDUAL BUTTON COMPONENTS FOR MIGRATION
// ============================================================================

interface MigratedSettingsButtonProps {
	systemId?: string;
	priority?: number;
	onClick?: () => void;
}

export function MigratedSettingsButton({
	systemId = 'unified-system',
	priority = 10,
	onClick,
}: MigratedSettingsButtonProps) {
	const { t } = useTranslation();
	const system = useFloatingButtonSystem(systemId);

	useEffect(() => {
		system.registerButton({
			id: 'migrated-settings',
			label: t('settings.button'),
			icon: <Settings className="h-6 w-6" />,
			onClick: onClick || (() => console.log('Settings clicked')),
			priority,
		});

		return () => {
			system.unregisterButton('migrated-settings');
		};
	}, [system, onClick, priority, t]);

	return null; // Rendered through floating system
}

interface MigratedFeedbackButtonProps {
	systemId?: string;
	priority?: number;
	onClick?: () => void;
}

export function MigratedFeedbackButton({
	systemId = 'unified-system',
	priority = 8,
	onClick,
}: MigratedFeedbackButtonProps) {
	const { t } = useTranslation();
	const system = useFloatingButtonSystem(systemId);

	useEffect(() => {
		system.registerButton({
			id: 'migrated-feedback',
			label: t('feedback.button_title'),
			icon: <MessageSquare className="h-6 w-6" />,
			onClick: onClick || (() => console.log('Feedback clicked')),
			priority,
		});

		return () => {
			system.unregisterButton('migrated-feedback');
		};
	}, [system, onClick, priority, t]);

	return null; // Rendered through floating system
}

// ============================================================================
// FLOATING BUTTON STACK WITH SYSTEM INTEGRATION
// ============================================================================

interface IntegratedFloatingStackProps {
	systemId?: string;
	position?: 'bottom-right' | 'bottom-left' | 'top-right' | 'top-left';
	gap?: number;
	children?: React.ReactNode;
	includeSystemButtons?: boolean;
}

export function IntegratedFloatingStack({
	systemId = 'integrated-stack',
	position = 'bottom-right',
	gap = 2,
	children,
	includeSystemButtons = true,
}: IntegratedFloatingStackProps) {
	const _system = useFloatingButtonSystem(systemId, { position, gap });

	const positionClasses = {
		'bottom-right': 'bottom-6 right-6',
		'bottom-left': 'bottom-6 left-6',
		'top-right': 'top-6 right-6',
		'top-left': 'top-6 left-6',
	};

	const flexDirection = position.startsWith('bottom') ? 'flex-col-reverse' : 'flex-col';
	const alignItems = position.endsWith('right') ? 'items-end' : 'items-start';

	return (
		<>
			{/* System buttons */}
			{includeSystemButtons && (
				<FloatingButtonManager systemId={systemId} position={position} gap={gap} />
			)}

			{/* Manual children */}
			{children && (
				<div
					className={`fixed z-50 flex ${flexDirection} ${alignItems} ${positionClasses[position]}`}
					style={{ gap: `${gap * 0.25}rem` }}
				>
					{children}
				</div>
			)}
		</>
	);
}

// ============================================================================
// MIGRATION UTILITIES
// ============================================================================

export function useUnifiedFloatingSystem(systemId: string = 'unified-system') {
	const system = useFloatingButtonSystem(systemId, {
		position: 'bottom-right',
		gap: 2,
		maxVisible: 10,
		collisionDetection: true,
	});

	const addSettingsButton = (onClick?: () => void, priority: number = 10) => {
		system.registerButton({
			id: 'settings',
			label: 'Settings',
			icon: <Settings className="h-6 w-6" />,
			onClick: onClick || (() => {}),
			priority,
		});
	};

	const addFeedbackButton = (onClick?: () => void, priority: number = 8) => {
		system.registerButton({
			id: 'feedback',
			label: 'Feedback',
			icon: <MessageSquare className="h-6 w-6" />,
			onClick: onClick || (() => {}),
			priority,
		});
	};

	const removeSettingsButton = () => {
		system.unregisterButton('settings');
	};

	const removeFeedbackButton = () => {
		system.unregisterButton('feedback');
	};

	return {
		...system,
		addSettingsButton,
		addFeedbackButton,
		removeSettingsButton,
		removeFeedbackButton,
	};
}
