'use client';

import React from 'react';
import { Button } from '@/components/ui';
import { Settings, MessageSquare } from 'lucide-react';

export function SimpleIntegratedButtons() {
	console.log('SimpleIntegratedButtons rendering...');

	return (
		<div className="fixed bottom-6 right-6 z-50 flex flex-col-reverse items-end gap-2">
			<Button
				size="icon"
				className="h-12 w-12 rounded-full shadow-md bg-background border hover:shadow-lg transition-all duration-200"
				onClick={() => {
					console.log('Settings clicked!');
					alert('Settings clicked!');
				}}
				aria-label="Settings"
			>
				<Settings className="h-4 w-4" />
			</Button>
			<Button
				size="icon"
				className="h-12 w-12 rounded-full shadow-md bg-background border hover:shadow-lg transition-all duration-200"
				onClick={() => {
					console.log('Feedback clicked!');
					alert('Feedback clicked!');
				}}
				aria-label="Feedback"
			>
				<MessageSquare className="h-4 w-4" />
			</Button>
		</div>
	);
}
