'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui';
import { Settings, MessageSquare, X } from 'lucide-react';
import { useTranslation } from '@/contexts/translation-context';

interface SimpleFloatingButtonsProps {
	includeSettings?: boolean;
	includeFeedback?: boolean;
}

export function SimpleFloatingButtons({
	includeSettings = true,
	includeFeedback = true,
}: SimpleFloatingButtonsProps) {
	const { t } = useTranslation();
	const [isSettingsOpen, setIsSettingsOpen] = useState(false);
	const [isFeedbackOpen, setIsFeedbackOpen] = useState(false);

	return (
		<>
			{/* Fixed Floating Buttons */}
			<div className="fixed bottom-6 right-6 z-50 flex flex-col-reverse items-end gap-2">
				{includeSettings && (
					<Button
						size="icon"
						variant="outline"
						className="h-12 w-12 rounded-full shadow-lg hover:shadow-xl transition-all duration-200"
						onClick={() => setIsSettingsOpen(true)}
						aria-label={t('settings.button')}
					>
						<Settings className="h-4 w-4" />
					</Button>
				)}
				{includeFeedback && (
					<Button
						size="icon"
						variant="outline"
						className="h-12 w-12 rounded-full shadow-lg hover:shadow-xl transition-all duration-200"
						onClick={() => setIsFeedbackOpen(true)}
						aria-label={t('feedback.button_title')}
					>
						<MessageSquare className="h-4 w-4" />
					</Button>
				)}
			</div>

			{/* Settings Modal */}
			{isSettingsOpen && (
				<div className="fixed inset-0 z-[1300] flex items-center justify-center">
					<div
						className="absolute inset-0 bg-black/20"
						onClick={() => setIsSettingsOpen(false)}
					/>
					<div className="relative z-10 bg-card rounded-lg shadow-lg p-6 max-w-md w-full mx-4 border border-border">
						<div className="flex items-center justify-between mb-4">
							<h2 className="text-lg font-semibold text-card-foreground">
								{t('settings.title')}
							</h2>
							<Button
								size="icon"
								variant="ghost"
								onClick={() => setIsSettingsOpen(false)}
								aria-label={t('settings.close')}
							>
								<X className="h-4 w-4" />
							</Button>
						</div>
						<div className="space-y-4">
							<div className="space-y-3">
								<div className="flex items-center justify-between">
									<label className="text-sm font-medium text-card-foreground">
										{t('settings.theme')}
									</label>
									<select className="px-3 py-1 border border-border rounded-md text-sm bg-background text-foreground">
										<option value="system">System</option>
										<option value="light">Light</option>
										<option value="dark">Dark</option>
									</select>
								</div>
								<div className="flex items-center justify-between">
									<label className="text-sm font-medium text-card-foreground">
										{t('settings.language')}
									</label>
									<select className="px-3 py-1 border border-border rounded-md text-sm bg-background text-foreground">
										<option value="en">English</option>
										<option value="vi">Tiếng Việt</option>
									</select>
								</div>
							</div>
							<div className="flex justify-end gap-2 pt-4 border-t border-border">
								<Button variant="outline" onClick={() => setIsSettingsOpen(false)}>
									{t('ui.cancel')}
								</Button>
								<Button onClick={() => setIsSettingsOpen(false)}>
									{t('ui.save')}
								</Button>
							</div>
						</div>
					</div>
				</div>
			)}

			{/* Feedback Modal */}
			{isFeedbackOpen && (
				<div className="fixed inset-0 z-[1300] flex items-center justify-center p-4">
					<div
						className="absolute inset-0 bg-black/20"
						onClick={() => setIsFeedbackOpen(false)}
					/>
					<div className="relative z-10 bg-card rounded-lg shadow-lg p-6 max-w-md w-full border border-border">
						<div className="flex items-center justify-between mb-4">
							<h2 className="text-lg font-semibold text-card-foreground">
								{t('feedback.title')}
							</h2>
							<Button
								size="icon"
								variant="ghost"
								onClick={() => setIsFeedbackOpen(false)}
								aria-label={t('settings.close')}
							>
								<X className="h-4 w-4" />
							</Button>
						</div>
						<div className="space-y-4">
							<div>
								<label className="block text-sm font-medium mb-2 text-card-foreground">
									{t('feedback.message')}
								</label>
								<textarea
									className="w-full p-3 border border-border rounded-md resize-none focus:ring-2 focus:ring-primary focus:border-transparent bg-background text-foreground"
									rows={4}
									placeholder={t('feedback.placeholder.message')}
								/>
							</div>
							<div className="flex justify-end gap-2 pt-4 border-t border-border">
								<Button variant="outline" onClick={() => setIsFeedbackOpen(false)}>
									{t('ui.cancel')}
								</Button>
								<Button onClick={() => setIsFeedbackOpen(false)}>
									{t('feedback.submit')}
								</Button>
							</div>
						</div>
					</div>
				</div>
			)}
		</>
	);
}
