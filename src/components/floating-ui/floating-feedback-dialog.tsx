'use client';

import { Button } from '@/components/ui';
import { useTranslation } from '@/contexts';
import { useFloatingUIElement } from '@/hooks/use-floating-ui';
import { motion } from 'framer-motion';
import { MessageSquare } from 'lucide-react';
import { useState } from 'react';
import { EnhancedFeedbackForm } from '@/components/feedback/enhanced-feedback-form';
import { floatingFeedbackButtonVariants } from '@/components/animations/feedback-animations';

interface FloatingFeedbackProps {
	id?: string;
	className?: string;
}

export function FloatingFeedback({ id = 'floating-feedback', className }: FloatingFeedbackProps) {
	const [isOpen, setIsOpen] = useState(false);
	const { t } = useTranslation();

	// Enhanced feedback button with animations
	const feedbackButton = (
		<motion.div
			variants={floatingFeedbackButtonVariants}
			initial="idle"
			whileHover="hover"
			whileTap="tap"
		>
			<Button
				size="icon"
				className="h-14 w-14 rounded-full shadow-lg hover:shadow-xl transition-shadow duration-200 bg-primary hover:bg-primary/90"
				onClick={() => setIsOpen(true)}
				title={t('feedback.button_title')}
				aria-label={t('feedback.button_title')}
			>
				<MessageSquare className="h-6 w-6" />
			</Button>
		</motion.div>
	);

	// Use floating UI element for button positioning
	const { show: _show, hide: _hide } = useFloatingUIElement(`${id}-button`, feedbackButton, {
		type: 'custom',
		priority: 'medium',
		position: 'bottom-right',
		coordinates: { bottom: 80, right: 16 }, // Settings (16) + button height (56) + gap (8) = 80
		animation: { type: 'scale', duration: 200 },
		autoShow: true,
		className,
		collisionDetection: true,
	});

	return (
		<>
			{/* Enhanced Feedback Form Modal */}
			<EnhancedFeedbackForm isOpen={isOpen} onClose={() => setIsOpen(false)} />
		</>
	);
}
