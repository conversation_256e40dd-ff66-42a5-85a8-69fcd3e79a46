// ============================================================================
// FLOATING UI EXPORTS
// ============================================================================

// Components
export {
	FloatingUIManager,
	FloatingUIPortal,
	floatingUIStyles,
	FloatingUIWrapper,
} from './floating-ui-manager';
export { FloatingFeedback } from './floating-feedback-dialog';
export {
	FloatingButtonManager,
	FloatingButtonStack,
	FloatingButtonWrapper,
	useFloatingButtonSystem,
	useFloatingButton,
} from './floating-button-manager';
export type { FloatingButtonItem, FloatingButtonSystemOptions } from './floating-button-manager';
export {
	UnifiedFloatingSystem,
	FloatingButtonRegistry,
	MigratedSettingsButton,
	MigratedFeedbackButton,
	IntegratedFloatingStack,
	useUnifiedFloatingSystem,
} from './unified-floating-system';
export { IntegratedFloatingButtons } from './integrated-floating-buttons';

// Context
export { FloatingUIProvider, useFloatingUI } from '@/contexts/floating-ui-context';

// Hooks
export {
	useFloatingDropdown,
	useFloatingGuidance,
	useFloatingModal,
	useFloatingNotification,
	useFloatingSettings,
	useFloatingTooltip,
	useFloatingUIElement,
	useFloatingUIManager,
	useResponsiveFloatingUI,
} from '@/hooks/use-floating-ui';

// Types
export type {
	FloatingAnimationConfig,
	FloatingCoordinates,
	FloatingDimensions,
	FloatingElement,
	FloatingGuidanceOptions,
	FloatingPosition,
	FloatingPriority,
	FloatingSettingsOptions,
	FloatingUIActions,
	FloatingUIConfig,
	FloatingUIContextType,
	FloatingUIManagerProps,
	FloatingUIState,
	FloatingUIType,
	UseFloatingUIOptions,
	UseFloatingUIReturn,
} from '@/types/floating-ui';

export { DEFAULT_FLOATING_CONFIG } from '@/types/floating-ui';
