'use client';

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, But<PERSON> } from '@/components/ui';
import { useTranslation } from '@/contexts';
import { useDOMFloating } from '@/hooks/use-dom-floating';
import { HelpCircle, Lightbulb, CheckCircle2, X } from 'lucide-react';
import { useState } from 'react';

interface GuidanceStep {
	key: string;
	icon?: React.ComponentType<any>;
}

interface PageGuidanceProps {
	titleKey: string;
	steps: GuidanceStep[];
	tipKey?: string;
	requirementKey?: string;
	className?: string;
	defaultOpen?: boolean;
	id?: string; // Unique identifier for this guidance instance
}

export function PageGuidance({
	titleKey,
	steps,
	tipKey,
	requirementKey,
	className = '',
	defaultOpen = true,
	id = 'page-guidance',
}: PageGuidanceProps) {
	const { t } = useTranslation();
	const [isOpen, setIsOpen] = useState(defaultOpen);

	// Get translated values outside the portal to avoid context issues
	const translatedTitle = t(title<PERSON><PERSON> as any);
	const translatedSteps = steps.map((step) => ({
		...step,
		translatedText: t(step.key as any),
	}));
	const translatedTip = tipKey ? t(tipKey as any) : undefined;
	const translatedRequirement = requirementKey ? t(requirementKey as any) : undefined;
	const translatedGuidanceButton = t('guidance.button');

	// Guidance content component
	const guidanceContent = (
		<div className="floating-guidance-panel">
			{!isOpen ? (
				// Guidance button
				<Button
					size="icon"
					className="h-14 w-14 rounded-full shadow-lg hover:shadow-xl transition-all duration-200 hover:scale-105"
					onClick={() => setIsOpen(true)}
					title={translatedGuidanceButton}
					aria-label={translatedGuidanceButton}
				>
					<HelpCircle className="h-6 w-6" />
				</Button>
			) : (
				// Guidance panel
				<Card className="border bg-background shadow-lg max-w-sm">
					<CardHeader className="pb-4">
						<div className="flex items-center justify-between">
							<CardTitle className="flex items-center gap-3">
								<div className="p-2 rounded-lg bg-primary text-primary-foreground">
									<Lightbulb className="h-5 w-5" />
								</div>
								{translatedTitle}
							</CardTitle>
							<Button
								size="icon"
								variant="ghost"
								className="h-8 w-8"
								onClick={() => setIsOpen(false)}
							>
								<X className="h-4 w-4" />
							</Button>
						</div>
					</CardHeader>
					<CardContent className="space-y-4">
						{/* Steps */}
						<div className="space-y-3">
							{translatedSteps.map((step, index) => (
								<div key={step.key} className="flex items-start gap-3">
									<div className="flex-shrink-0 w-6 h-6 rounded-full bg-primary text-primary-foreground flex items-center justify-center mt-0.5">
										{step.icon ? (
											<step.icon className="h-3 w-3" />
										) : (
											<span className="text-xs font-medium">{index + 1}</span>
										)}
									</div>
									<p className="text-sm text-muted-foreground leading-relaxed">
										{step.translatedText}
									</p>
								</div>
							))}
						</div>

						{/* Tip */}
						{translatedTip && (
							<div className="p-3 rounded-lg bg-amber-50 border border-amber-200 dark:bg-amber-900 dark:border-amber-800">
								<div className="flex items-start gap-2">
									<Lightbulb className="h-4 w-4 text-amber-600 dark:text-amber-400 mt-0.5 flex-shrink-0" />
									<p className="text-sm text-amber-800 dark:text-amber-200">
										{translatedTip}
									</p>
								</div>
							</div>
						)}

						{/* Requirement */}
						{translatedRequirement && (
							<div className="p-3 rounded-lg bg-blue-50 border border-blue-200 dark:bg-blue-900 dark:border-blue-800">
								<div className="flex items-start gap-2">
									<CheckCircle2 className="h-4 w-4 text-blue-600 dark:text-blue-400 mt-0.5 flex-shrink-0" />
									<p className="text-sm text-blue-800 dark:text-blue-200">
										{translatedRequirement}
									</p>
								</div>
							</div>
						)}
					</CardContent>
				</Card>
			)}
		</div>
	);

	// Use DOM floating UI system
	useDOMFloating(id, guidanceContent, {
		position: { bottom: 80, right: 16 },
		zIndex: 1100,
		autoShow: defaultOpen,
		className,
	});

	return null; // Content is rendered through DOM floating UI system
}
