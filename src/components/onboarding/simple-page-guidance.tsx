'use client';

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, But<PERSON> } from '@/components/ui';
import { useTranslation } from '@/contexts';
import { HelpCircle, Lightbulb, CheckCircle2, X } from 'lucide-react';
import { useState } from 'react';

interface GuidanceStep {
	key: string;
	icon?: React.ComponentType<any>;
}

interface SimplePageGuidanceProps {
	titleKey: string;
	steps: GuidanceStep[];
	tipKey?: string;
	requirementKey?: string;
	className?: string;
	defaultOpen?: boolean;
	id?: string;
}

export function SimplePageGuidance({
	titleKey,
	steps,
	tipKey,
	requirementKey,
	className = '',
	defaultOpen = true,
	id = 'simple-page-guidance',
}: SimplePageGuidanceProps) {
	const { t } = useTranslation();
	const [isOpen, setIsOpen] = useState(defaultOpen);

	if (!isOpen) {
		return (
			<div
				style={{
					position: 'fixed',
					bottom: 80,
					right: 16,
					zIndex: 1100,
				}}
			>
				<Button
					size="icon"
					className="h-14 w-14 rounded-full shadow-lg bg-blue-600 hover:bg-blue-700 text-white hover:shadow-xl transition-all duration-200 hover:scale-105"
					onClick={() => setIsOpen(true)}
					title={t('guidance.button')}
					aria-label={t('guidance.button')}
				>
					<HelpCircle className="h-6 w-6" />
				</Button>
			</div>
		);
	}

	return (
		<div
			style={{
				position: 'fixed',
				bottom: 80,
				right: 16,
				zIndex: 1100,
				maxWidth: '400px',
				width: '90vw',
			}}
			className={className}
		>
			<Card className="shadow-xl border-2 border-blue-200 dark:border-blue-800 bg-white/95 dark:bg-gray-900/95 backdrop-blur-sm">
				<CardHeader className="pb-3 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 border-b border-blue-100 dark:border-blue-800">
					<div className="flex items-center justify-between">
						<CardTitle className="text-lg font-semibold text-blue-800 dark:text-blue-200 flex items-center gap-2">
							<Lightbulb className="h-5 w-5" />
							{t(titleKey as any)}
						</CardTitle>
						<Button
							variant="ghost"
							size="icon"
							onClick={() => setIsOpen(false)}
							className="h-8 w-8 text-blue-600 hover:text-blue-800 hover:bg-blue-100 dark:text-blue-400 dark:hover:text-blue-200 dark:hover:bg-blue-900/50"
						>
							<X className="h-4 w-4" />
						</Button>
					</div>
				</CardHeader>
				<CardContent className="pt-4 space-y-4">
					<div className="space-y-3">
						{steps.map((step, index) => {
							const IconComponent = step.icon || CheckCircle2;
							return (
								<div key={step.key} className="flex items-start gap-3">
									<div className="flex-shrink-0 w-6 h-6 rounded-full bg-blue-100 dark:bg-blue-900/50 flex items-center justify-center mt-0.5">
										<IconComponent className="h-3 w-3 text-blue-600 dark:text-blue-400" />
									</div>
									<p className="text-sm text-gray-700 dark:text-gray-300 leading-relaxed">
										{t(step.key as any)}
									</p>
								</div>
							);
						})}
					</div>

					{tipKey && (
						<div className="mt-4 p-3 bg-amber-50 dark:bg-amber-900/20 rounded-lg border border-amber-200 dark:border-amber-800">
							<p className="text-sm text-amber-800 dark:text-amber-200">
								<strong>💡 {t('guidance.tip')}:</strong> {t(tipKey as any)}
							</p>
						</div>
					)}

					{requirementKey && (
						<div className="mt-4 p-3 bg-red-50 dark:bg-red-900/20 rounded-lg border border-red-200 dark:border-red-800">
							<p className="text-sm text-red-800 dark:text-red-200">
								<strong>⚠️ {t('guidance.requirement')}:</strong> {t(requirementKey as any)}
							</p>
						</div>
					)}
				</CardContent>
			</Card>
		</div>
	);
}
