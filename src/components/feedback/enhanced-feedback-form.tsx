'use client';

import { <PERSON><PERSON>, <PERSON>, Loading<PERSON>pinner, <PERSON>are<PERSON>, Translate } from '@/components/ui';
import { FEEDBACK_SECTION_LOADING_KEYS } from '@/constants/loading-keys';
import { LOADING_SCOPES } from '@/constants/loading-scopes';
import { useScopedLoading, useTranslation } from '@/contexts';
import { motion, AnimatePresence } from 'framer-motion';
import { MessageSquare, Send, X, CheckCircle, AlertCircle } from 'lucide-react';
import { FormEvent, useState } from 'react';
import { toast } from 'sonner';
import {
	feedbackModalVariants,
	feedbackFormVariants,
	feedbackFieldVariants,
	feedbackButtonVariants,
	feedbackStateVariants,
	backdropVariants,
} from '@/components/animations/feedback-animations';

interface EnhancedFeedbackFormProps {
	isOpen: boolean;
	onClose: () => void;
}

export function EnhancedFeedbackForm({ isOpen, onClose }: EnhancedFeedbackFormProps) {
	const [feedbackMessage, setFeedbackMessage] = useState('');
	const [submitState, setSubmitState] = useState<'idle' | 'success' | 'error'>('idle');
	const { setLoading: setSubmittingLoading, getLoading: getSubmittingLoading } = useScopedLoading(
		LOADING_SCOPES.FEEDBACK_SECTION
	);
	const { t } = useTranslation();

	const handleFeedbackSubmit = async (e: FormEvent) => {
		e.preventDefault();

		// Validate required fields
		if (!feedbackMessage.trim()) {
			toast.error(t('toast.missing_info'), {
				description: t('toast.missing_info_desc'),
			});
			return;
		}

		setSubmittingLoading(FEEDBACK_SECTION_LOADING_KEYS.SUBMIT_FEEDBACK, true);
		setSubmitState('idle');

		try {
			const response = await fetch('/api/feedback', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
				},
				body: JSON.stringify({
					message: feedbackMessage,
				}),
			});

			if (!response.ok) {
				throw new Error('Failed to submit feedback');
			}

			setSubmitState('success');
			toast.success(t('toast.feedback_sent'), {
				description: t('toast.feedback_sent_desc'),
			});

			// Reset form and close after delay
			setTimeout(() => {
				setFeedbackMessage('');
				setSubmitState('idle');
				onClose();
			}, 2000);
		} catch (error) {
			console.error('Error submitting feedback:', error);
			setSubmitState('error');
			toast.error(t('toast.feedback_error'), {
				description: t('toast.feedback_error_desc'),
			});
		} finally {
			setSubmittingLoading(FEEDBACK_SECTION_LOADING_KEYS.SUBMIT_FEEDBACK, false);
		}
	};

	const isLoading = getSubmittingLoading('submitFeedback');

	if (!isOpen) return null;

	return (
		<AnimatePresence>
			<motion.div
				className="fixed inset-0 z-[1300] flex items-center justify-center p-4"
				variants={backdropVariants}
				initial="hidden"
				animate="visible"
				exit="exit"
			>
				<motion.div
					className="absolute inset-0 bg-black/20 backdrop-blur-sm"
					onClick={onClose}
				/>
				<motion.div
					variants={feedbackModalVariants}
					initial="hidden"
					animate="visible"
					exit="exit"
					className="relative z-10"
				>
					<Card className="w-full max-w-md p-6 bg-background/95 backdrop-blur-sm border shadow-xl">
						{/* Header */}
						<motion.div
							variants={feedbackFormVariants}
							className="flex items-center justify-between mb-6"
						>
							<h3 className="text-xl font-semibold flex items-center gap-2">
								<div className="p-2 rounded-lg bg-primary text-primary-foreground">
									<MessageSquare className="h-4 w-4" />
								</div>
								<Translate text="feedback.title" />
							</h3>
							<motion.div
								whileHover={{ scale: 1.1 }}
								whileTap={{ scale: 0.9 }}
							>
								<Button
									variant="ghost"
									size="sm"
									onClick={onClose}
									className="h-8 w-8 p-0 hover:bg-muted"
								>
									<X className="h-4 w-4" />
								</Button>
							</motion.div>
						</motion.div>

						{/* Success/Error States */}
						<AnimatePresence mode="wait">
							{submitState === 'success' && (
								<motion.div
									variants={feedbackStateVariants}
									initial="hidden"
									animate="visible"
									exit="exit"
									className="text-center py-8"
								>
									<CheckCircle className="h-12 w-12 text-green-500 mx-auto mb-4" />
									<h4 className="text-lg font-medium text-green-700 dark:text-green-400 mb-2">
										{t('feedback.success.title')}
									</h4>
									<p className="text-sm text-muted-foreground">
										{t('feedback.success.description')}
									</p>
								</motion.div>
							)}

							{submitState === 'error' && (
								<motion.div
									variants={feedbackStateVariants}
									initial="hidden"
									animate="visible"
									exit="exit"
									className="text-center py-4 mb-4"
								>
									<AlertCircle className="h-8 w-8 text-red-500 mx-auto mb-2" />
									<p className="text-sm text-red-600 dark:text-red-400">
										{t('feedback.error.description')}
									</p>
								</motion.div>
							)}

							{submitState === 'idle' && (
								<motion.form
									variants={feedbackFormVariants}
									initial="hidden"
									animate="visible"
									className="space-y-4"
									onSubmit={handleFeedbackSubmit}
								>
									<motion.div variants={feedbackFieldVariants}>
										<label htmlFor="feedback" className="block text-sm font-medium mb-2">
											<Translate text="feedback.message" />
										</label>
										<motion.div
											whileFocus="focus"
											variants={feedbackFieldVariants}
										>
											<Textarea
												id="feedback"
												value={feedbackMessage}
												onChange={(e) => setFeedbackMessage(e.target.value)}
												className="w-full min-h-[120px] focus:ring-2 focus:ring-primary/20 transition-all duration-200 resize-none"
												placeholder={t('feedback.placeholder.message')}
												required
												disabled={isLoading}
											/>
										</motion.div>
									</motion.div>

									<motion.div
										variants={feedbackFieldVariants}
										className="flex justify-end gap-2"
									>
										<motion.div
											variants={feedbackButtonVariants}
											whileHover="hover"
											whileTap="tap"
										>
											<Button
												type="button"
												variant="outline"
												onClick={onClose}
												disabled={isLoading}
												size="sm"
											>
												Cancel
											</Button>
										</motion.div>
										<motion.div
											variants={feedbackButtonVariants}
											whileHover={!isLoading ? "hover" : undefined}
											whileTap={!isLoading ? "tap" : undefined}
											animate={isLoading ? "loading" : "idle"}
										>
											<Button
												type="submit"
												disabled={isLoading || !feedbackMessage.trim()}
												size="sm"
												className="min-w-[80px]"
											>
												{isLoading ? (
													<LoadingSpinner size="sm" />
												) : (
													<>
														<Send className="h-4 w-4 mr-2" />
														Send
													</>
												)}
											</Button>
										</motion.div>
									</motion.div>
								</motion.form>
							)}
						</AnimatePresence>
					</Card>
				</motion.div>
			</motion.div>
		</AnimatePresence>
	);
}
