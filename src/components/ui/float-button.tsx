'use client';

import {
	Button,
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuTrigger,
} from '@/components/ui';
import { FloatingButtonWrapper } from '@/components/floating-ui/floating-button-manager';
import { Menu, Plus, X } from 'lucide-react';
import { useState } from 'react';

interface FloatButtonProps {
	items: {
		label: string;
		icon: React.ReactNode;
		onClick: () => void;
		priority?: number; // Add priority for ordering
	}[];
	className?: string;
}

export function FloatButton({ items, className }: FloatButtonProps) {
	const [open, setOpen] = useState(false);

	// Sort items by priority (higher priority first)
	const sortedItems = [...items].sort((a, b) => (b.priority || 0) - (a.priority || 0));

	return (
		<FloatingButtonWrapper className={className}>
			{open && (
				<div className="flex flex-col gap-2">
					{sortedItems.map((item, index) => (
						<Button
							key={index}
							size="icon"
							variant="outline"
							className="bg-background shadow-md h-12 w-12 rounded-full"
							onClick={() => {
								setOpen(false);
								item.onClick();
							}}
						>
							{item.icon}
							<span className="sr-only">{item.label}</span>
						</Button>
					))}
				</div>
			)}

			<Button
				size="icon"
				className="h-14 w-14 rounded-full shadow-lg"
				onClick={() => setOpen(!open)}
			>
				{open ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
			</Button>
		</FloatingButtonWrapper>
	);
}

export function FloatMenuButton({ items, className }: FloatButtonProps) {
	// Sort items by priority (higher priority first)
	const sortedItems = [...items].sort((a, b) => (b.priority || 0) - (a.priority || 0));

	return (
		<FloatingButtonWrapper className={className}>
			<DropdownMenu>
				<DropdownMenuTrigger asChild>
					<Button size="icon" className="h-14 w-14 rounded-full shadow-lg">
						<Plus className="h-6 w-6" />
					</Button>
				</DropdownMenuTrigger>
				<DropdownMenuContent align="end" className="mb-1 mr-1">
					{sortedItems.map((item, index) => (
						<DropdownMenuItem
							key={index}
							onClick={item.onClick}
							className="cursor-pointer"
						>
							<div className="flex items-center gap-2">
								{item.icon}
								<span>{item.label}</span>
							</div>
						</DropdownMenuItem>
					))}
				</DropdownMenuContent>
			</DropdownMenu>
		</FloatingButtonWrapper>
	);
}
