'use client';

import { Translate } from '@/components/ui';
import { motion } from 'framer-motion';

export function FeedbackSection() {
	return (
		<div className="space-y-4">
			<h2 className="text-2xl font-bold text-center">
				<Translate text="feedback.title" />
			</h2>
			<motion.div
				initial={{ opacity: 0, y: -10 }}
				animate={{ opacity: 1, y: 0 }}
				transition={{ duration: 0.3 }}
				className="text-center text-muted-foreground"
			>
				<p>
					<Translate text="feedback.global_notice" />
				</p>
			</motion.div>
		</div>
	);
}
